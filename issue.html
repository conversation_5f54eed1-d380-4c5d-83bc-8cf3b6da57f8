<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Issue - RISE STEM</title>
  <link href="/src/style.css" rel="stylesheet">
  <link rel="stylesheet" href="src/animations.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html {
      scroll-behavior: smooth;
    }
    .flip-card .flip-card-inner {
      perspective: 1000px;
    }
    .flip-card .flip-card-front,
    .flip-card .flip-card-back {
      backface-visibility: hidden;
    }
    .flip-card .flip-card-back {
      transform: rotateY(180deg);
    }
    .flip-card {
      transition: transform 0.3s ease;
    }
    .flip-card:hover {
      transform: translateY(-10px);
    }
    .flip-card-front {
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    .flip-card:hover .flip-card-front {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    .flip-card-back {
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    .flip-card:hover .flip-card-back {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    .flip-card img {
      transition: transform 0.3s ease;
    }
    .flip-card:hover img {
      transform: scale(1.1);
    }
    @media (min-width: 768px) {
      .flip-card {
        min-width: 380px;
        max-width: 460px;
        width: 100%;
      }
      .flip-card-inner {
        min-width: 380px;
        max-width: 460px;
        width: 100%;
      }
    }
    @media (min-width: 1024px) {
      .flip-card {
        min-width: 420px;
        max-width: 520px;
        width: 100%;
      }
      .flip-card-inner {
        min-width: 420px;
        max-width: 520px;
        width: 100%;
      }
    }
    .flip-card,
    .flip-card-inner,
    .flip-card-front,
    .flip-card-back {
      min-width: 0;
      box-sizing: border-box;
    }
    .flip-card-front, .flip-card-back {
      word-break: break-word;
      padding-left: 1.25rem;
      padding-right: 1.25rem;
    }
  </style>
</head>
<body>

<!-- Header -->
<div id="header-container"></div>
<script type="module" src="/scripts/loadHeader.js"></script>
<!-- Main Content -->
<main class="min-h-screen bg-gray-50 w-full">

  <!-- Hero Section -->
  <section class=" w-full">
    <div class="w-full h-[60vh] bg-[url('/public/enpeng_leading.jpeg')] bg-cover bg-[center_30%] bg-no-repeat flex items-center justify-center relative">
      <!-- Gradient Overlay -->
      <div class="h-[60vh] absolute inset-0 bg-black opacity-50"></div>
      <div class="flex flex-col text-center items-center justify-center px-6 sm:px-16 md:px-32 z-10">
        <h1 class="mt-16 text-3xl font-extrabold text-white md:text-4xl xl:text-5xl justify-center">
          Bridging the STEM Gap for Georgia's Youth
        </h1>
      </div>
    </div>
  </section>

  <!-- The Issue Section -->

  <section id="Issue" class="w-full px-6 sm:px-6 md:px-16 lg:px-24 -mt-8 z-20 relative pb-12 scroll-mt-24">
    <!--
  <div class="bg-white p-4 rounded-2xl border border-gray-300 shadow-md justify-center">
    <div class="flex justify-center text-center">
      <h2 class="text-3xl md:text-4xl font-poppins font-bold text-purple-950">
        The Issue in Brief
      </h2>
    </div>

    <div class="flex justify-center items-center pt-8 sm:px-16 lg:px-32 flex-col">

      <div class="bg-white w-full py-12">
        <div class="max-w-6xl mx-auto px-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">

            <div class="flip-card group bg-transparent rounded-xl flex flex-col items-center">
              <div class="flip-card-inner relative w-full h-80 transition-transform duration-500 [transform-style:preserve-3d] group-hover:[transform:rotateY(180deg)]">
                <div class="flip-card-front absolute w-full h-full bg-white rounded-xl shadow-lg flex flex-col items-center p-8 backface-hidden">
              <span class="material-symbols-outlined text-purple-900 w-24 h-24 text-[96px] mb-4">
                code_blocks
              </span>
                  <h3 class="text-3xl font-bold text-purple-900 mb-2 text-center">STEM Access Gaps</h3>
                </div>
                <div class="flip-card-back absolute w-full h-full bg-[#fcfbfd] rounded-xl shadow-lg flex flex-col items-center justify-center p-8 [transform:rotateY(180deg)] backface-hidden">
                  <p class="text-gray-500 text-center">Many students, especially those from low-income or underrepresented backgrounds, lack access to quality STEM education. This gap limits their opportunities to explore, develop, and excel in critical fields like science, technology, engineering, and math, creating disparities in future careers and innovation potential.</p>
                </div>
              </div>
            </div>

            <div class="flip-card group bg-transparent rounded-xl flex flex-col items-center">
              <div class="flip-card-inner relative w-full h-80 transition-transform duration-500 [transform-style:preserve-3d] group-hover:[transform:rotateY(180deg)]">
                <div class="flip-card-front absolute w-full h-full bg-white rounded-xl shadow-lg flex flex-col items-center p-8 backface-hidden">
              <span class="material-symbols-outlined text-purple-900 w-24 h-24 text-[96px] mb-4">
                group
              </span>
                  <h3 class="text-3xl font-bold text-purple-900 mb-2 text-center">Confidence</h3>
                </div>
                <div class="flip-card-back absolute w-full h-full bg-[#fcfbfd] rounded-xl shadow-lg flex flex-col items-center justify-center p-8 [transform:rotateY(180deg)] backface-hidden">
                  <p class="text-gray-500 text-center">Many students, particularly from marginalized communities, face confidence gaps in STEM subjects. Without encouragement and support, they may doubt their abilities, leading to disengagement and limiting their future opportunities in these critical fields.</p>
                </div>
              </div>
            </div>

            <div class="flip-card group bg-transparent rounded-xl flex flex-col items-center">
              <div class="flip-card-inner relative w-full h-80 transition-transform duration-500 [transform-style:preserve-3d] group-hover:[transform:rotateY(180deg)]">
                <div class="flip-card-front absolute w-full h-full bg-white rounded-xl shadow-lg flex flex-col items-center p-8 backface-hidden">
              <span class="material-symbols-outlined text-purple-900 w-24 h-24 text-[96px] mb-4">
                engineering
              </span>
                  <h3 class="text-3xl font-bold text-purple-900 mb-2 text-center">Skills Gap</h3>
                </div>
                <div class="flip-card-back absolute w-full h-full bg-[#fcfbfd] rounded-xl shadow-lg flex flex-col items-center justify-center p-8 [transform:rotateY(180deg)] backface-hidden">
                  <p class="text-gray-500 text-center">As technology evolves, there's a growing demand for STEM skills. Unfortunately, many students, especially those in underserved schools, lack access to the resources and hands-on experiences needed to develop these crucial skills, widening the gap between academic learning and workforce readiness.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="pt-10 flex justify-center">
          <button
                  id="continueReadingBtn"
                  type="button"
                  class="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-xl bg-white shadow-sm text-gray-500 transition active:translate-y-0.5 hover:scale-110 hover:border-gray-300 focus:outline-none"
          >
            Continue reading to learn more

            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        <script>
          document.getElementById('continueReadingBtn').addEventListener('click', function(event) {
            const target = document.getElementById('stem-skills');
            if (target) {
              target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          });
        </script>
      </div>
    </div>
  </div>
  -->

    <div class="bg-white p-8 rounded-2xl border border-gray-300 -mt-8 z-30 shadow-md flex flex-col items-center justify-center">

      <h3 id="stem-skills" class="text-3xl font-bold text-center text-purple-950 mb-2">STEM Skills Are in High Demand<sup id="fnref1"><a href="#fn1" class="text-purple-700">[1]</a></sup></h3>
      <div class="flex flex-col items-center text-gray-500">
        <p class="mb-8 text-center max-w-xl">
          STEM careers are growing much faster than most fields. By 2033, the U.S. Bureau of Labor Statistics projects:
        </p>
        <div class="flex flex-col sm:flex-row gap-10 items-center justify-center w-full">
          <div class="flex flex-col items-center">
            <span class="text-5xl md:text-6xl font-bold text-purple-950">10%</span>
            <span class="text-purple-700 font-semibold mt-2">GROWTH</span>
            <p class="text-center mt-2 max-w-xs">Projected increase in STEM jobs</p>
          </div>
          <div class="flex flex-col items-center">
            <span class="text-5xl md:text-6xl font-bold text-purple-950">$100K</span>
            <span class="text-purple-700 font-semibold mt-2">MEDIAN SALARY</span>
            <p class="text-center mt-2 max-w-xs">Median STEM wages, well above the national average</p>
          </div>
        </div>

        <p class="mt-8 text-center max-w-xl">
          In Georgia, some STEM careers are considered “hot jobs” because they offer strong growth, high salaries, and plenty of opportunities. Explore a few examples below:<sup id="fnref2"><a href="#fn2" class="text-purple-700">[2]</a></sup>
        </p>

        <!-- Hot Careers Cards Section (SVG Hot Badge) -->
        <div class="flex flex-wrap gap-8 justify-center mt-6">
          <!-- Mechanical Engineer Card -->
          <div class="relative w-64 h-56 bg-white border border-gray-300 rounded-2xl shadow-md flex flex-col justify-between p-6">
            <div>
              <span class="text-xl font-semibold text-purple-950 mb-2 block">Mechanical Engineer</span>
              <span class="text-gray-500 text-sm">Design and develop mechanical systems for a range of industries.</span>
            </div>
            <div class="hot-badge-group">
            <span class="hot-badge-svg" tabindex="0" style="display:inline-block; width:40px; height:40px;">
              <svg viewBox="0 0 80 80" width="40" height="40" xmlns="http://www.w3.org/2000/svg" aria-label="Hot Job Badge">
                <g>
                  <polygon
                          points="40,5 49,14 62,11 65,23 77,27 72,39 78,50 67,54 67,67 54,65 48,77 40,69 32,77 26,65 13,67 13,54 2,50 8,39 3,27 15,23 18,11 31,14"
                          fill="#F43F5E"
                  />
                  <text x="50%" y="54%" text-anchor="middle" fill="#FFF" font-family="Arial, Helvetica, sans-serif" font-size="24" font-weight="bold" dy=".3em" letter-spacing="1">
                    HOT
                  </text>
                </g>
              </svg>
            </span>
              <span class="hot-tooltip">
              “Hot Job” = Above-average growth, above-average pay, 100+ annual openings in Georgia
            </span>
            </div>
          </div>
          <!-- Software Developer Card -->
          <div class="relative w-64 h-56 bg-white border border-gray-300 rounded-2xl shadow-md flex flex-col justify-between p-6">
            <div>
              <span class="text-xl font-semibold text-purple-950 mb-2 block">Software Developer</span>
              <span class="text-gray-500 text-sm">Create applications and systems for computers and mobile devices.</span>
            </div>
            <div class="hot-badge-group">
            <span class="hot-badge-svg" tabindex="0" style="display:inline-block; width:40px; height:40px;">
              <svg viewBox="0 0 80 80" width="40" height="40" xmlns="http://www.w3.org/2000/svg" aria-label="Hot Job Badge">
                <g>
                  <polygon
                          points="40,5 49,14 62,11 65,23 77,27 72,39 78,50 67,54 67,67 54,65 48,77 40,69 32,77 26,65 13,67 13,54 2,50 8,39 3,27 15,23 18,11 31,14"
                          fill="#F43F5E"
                  />
                  <text x="50%" y="54%" text-anchor="middle" fill="#FFF" font-family="Arial, Helvetica, sans-serif" font-size="24" font-weight="bold" dy=".3em" letter-spacing="1">
                    HOT
                  </text>
                </g>
              </svg>
            </span>
              <span class="hot-tooltip">
              “Hot Job” = Above-average growth, above-average pay, 100+ annual openings in Georgia
            </span>
            </div>
          </div>
          <!-- Web Developer Card -->
          <div class="relative w-64 h-56 bg-white border border-gray-300 rounded-2xl shadow-md flex flex-col justify-between p-6">
            <div>
              <span class="text-xl font-semibold text-purple-950 mb-2 block">Web Developer</span>
              <span class="text-gray-500 text-sm">Build and maintain websites and web applications.</span>
            </div>
            <div class="hot-badge-group">
            <span class="hot-badge-svg" tabindex="0" style="display:inline-block; width:40px; height:40px;">
              <svg viewBox="0 0 80 80" width="40" height="40" xmlns="http://www.w3.org/2000/svg" aria-label="Hot Job Badge">
                <g>
                  <polygon
                          points="40,5 49,14 62,11 65,23 77,27 72,39 78,50 67,54 67,67 54,65 48,77 40,69 32,77 26,65 13,67 13,54 2,50 8,39 3,27 15,23 18,11 31,14"
                          fill="#F43F5E"
                  />
                  <text x="50%" y="54%" text-anchor="middle" fill="#FFF" font-family="Arial, Helvetica, sans-serif" font-size="24" font-weight="bold" dy=".3em" letter-spacing="1">
                    HOT
                  </text>
                </g>
              </svg>
            </span>
              <span class="hot-tooltip">
              “Hot Job” = Above-average growth, above-average pay, 100+ annual openings in Georgia
            </span>
            </div>
          </div>
          <!-- Electronics Engineering Technician Card -->
          <div class="relative w-64 h-56 bg-white border border-gray-300 rounded-2xl shadow-md flex flex-col justify-between p-6">
            <div>
              <span class="text-xl font-semibold text-purple-950 mb-2 block">Electronics Eng. Technician</span>
              <span class="text-gray-500 text-sm">Test, repair, and maintain electronic equipment and systems.</span>
            </div>
            <div class="hot-badge-group">
            <span class="hot-badge-svg" tabindex="0" style="display:inline-block; width:40px; height:40px;">
              <svg viewBox="0 0 80 80" width="40" height="40" xmlns="http://www.w3.org/2000/svg" aria-label="Hot Job Badge">
                <g>
                  <polygon
                          points="40,5 49,14 62,11 65,23 77,27 72,39 78,50 67,54 67,67 54,65 48,77 40,69 32,77 26,65 13,67 13,54 2,50 8,39 3,27 15,23 18,11 31,14"
                          fill="#F43F5E"
                  />
                  <text x="50%" y="54%" text-anchor="middle" fill="#FFF" font-family="Arial, Helvetica, sans-serif" font-size="24" font-weight="bold" dy=".3em" letter-spacing="1">
                    HOT
                  </text>
                </g>
              </svg>
            </span>
              <span class="hot-tooltip">
              “Hot Job” = Above-average growth, above-average pay, 100+ annual openings in Georgia
            </span>
            </div>
          </div>
        </div>
        <p class="mt-8 text-center max-w-xl">
          These careers are just a few examples of the many opportunities available in Georgia’s growing STEM economy.
        </p>
      </div>
    </div>

    <!-- Required CSS for hot badge tooltip -->
    <style>
      .hot-badge-group {
        position: absolute; bottom: 1rem; right: 1rem;
        display: flex; flex-direction: column; align-items: flex-end;
      }
      .hot-tooltip {
        opacity: 0;
        pointer-events: none;
        position: absolute;
        bottom: 2.5rem;
        right: 0;
        background: #fff;
        color: #6a7282;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        box-shadow: 0 2px 8px #0001;
        font-size: 0.95rem;
        padding: 0.5rem 0.75rem;
        z-index: 10;
        transition: opacity 0.2s;
        width: 210px;
        text-align: left;
      }
      .hot-badge-group:hover .hot-tooltip,
      .hot-badge-group:focus-within .hot-tooltip {
        opacity: 1;
        pointer-events: auto;
      }
    </style>

    <div class="bg-white p-8 rounded-2xl border mt-8 border-gray-300 z-30 shadow-md flex flex-col items-center justify-center">
      <h3 class="text-3xl font-bold text-center text-purple-950 mb-4">Too Many Georgia Students Are Still Falling Behind<sup id="fnref3"><a href="#fn3" class="text-purple-700">[3]</a></sup></h3>
      <div class="flex flex-col items-center text-gray-500">
        <p class="mb-6 text-center max-w-xl">
          Despite the opportunities in STEM, far too many students aren’t being equipped to pursue them. In 2024, only:
        </p>
        <!-- Change these variables for size -->
        <div class="flex flex-col items-center justify-center w-full mb-6">
          <svg
                  id="donutChart"
                  width="200"
                  height="200"
                  viewBox="0 0 200 200"
                  class="block"
          >
            <!-- Track -->
            <circle
                    cx="100" cy="100" r="70"
                    fill="none"
                    stroke="#eee"
                    stroke-width="28"
                    stroke-linecap="butt"
            />
            <!-- Animated Arc -->
            <circle
                    id="donutArc"
                    cx="100" cy="100" r="70"
                    fill="none"
                    stroke="#7c3aed"
                    stroke-width="28"
                    stroke-linecap="butt"
                    stroke-dasharray="439.8229715"
                    stroke-dashoffset="439.8229715"
                    style="transition: stroke-dashoffset 0.2s linear"
                    transform="rotate(-90 100 100)"
            />
            <!-- Percentage Text -->
            <text
                    id="donutText"
                    x="100"
                    y="112"
                    text-anchor="middle"
                    font-size="48"
                    font-weight="bold"
                    fill="#3b225e"
                    class="font-bold"
            >0%</text>
          </svg>
          <p class="text-center mt-2 max-w-xs">
            of 5th graders reached science proficiency on the Georgia Milestones assessment
          </p>
        </div>
        <script>
          // Donut chart config
          const percentage = 27;
          const duration = 1200; // ms
          const radius = 70; // 10px larger than before
          const circumference = 2 * Math.PI * radius;
          const arc = document.getElementById('donutArc');
          const text = document.getElementById('donutText');
          let animationStarted = false;

          function animate(ts) {
            if (!animate.start) animate.start = ts;
            const elapsed = ts - animate.start;
            const t = Math.min(elapsed / duration, 1);
            const current = Math.round(t * percentage);
            const offset = circumference * (1 - current / 100);
            arc.setAttribute('stroke-dashoffset', offset);
            text.textContent = current + '%';
            if (t < 1) {
              requestAnimationFrame(animate);
            }
          }

          // Prepare SVG for animation
          arc.setAttribute('stroke-dasharray', circumference);
          arc.setAttribute('stroke-dashoffset', circumference);

          // Intersection Observer to trigger animation when in view
          const svg = document.getElementById('donutChart');
          const observer = new window.IntersectionObserver(
                  (entries, observer) => {
                    entries.forEach(entry => {
                      if (entry.isIntersecting && !animationStarted) {
                        animationStarted = true;
                        requestAnimationFrame(animate);
                        observer.disconnect();
                      }
                    });
                  },
                  { threshold: 0.2 }
          );
          observer.observe(svg);
        </script>
        <p class="text-center max-w-xl">
          This isn’t just a learning gap; it’s an opportunity gap. And it’s costing Georgia future innovators, leaders, and change-makers.
        </p>
      </div>

      <h3 class="text-3xl font-bold text-center text-purple-950 mt-10 mb-4">
        Learning Happens Beyond the Classroom<sup id="fnref4"><a href="#fn4" class="text-purple-700">[4]</a></sup>
      </h3>
      <div class="text-gray-500 space-y-4 max-w-7xl flex flex-col items-center justify-center text-center">
        <p class="max-w-xl w-full text-center">
          Students spend the majority of their waking hours outside of school. Afterschool time is a powerful window for hands-on, curiosity-driven learning.
        </p>

      </div>
      <div class="text-gray-500 space-y-4 max-w-7xl flex flex-col items-center justify-center text-center px-2 sm:px-4">
        <!-- Stacked timeline with bar chart -->
        <div class="flex flex-col sm:flex-row items-center justify-center w-full">
          <!-- Left: Percentage -->
          <div class="flex flex-col items-center justify-center flex-1 mb-4 sm:mb-6">
            <span class="text-5xl md:text-6xl font-bold text-purple-950">80%</span>
            <p class="text-center mt-2 max-w-xs">
              of student's waking hours are spent outside of school
            </p>
          </div>
          <!-- Divider -->
          <div class="hidden sm:block mx-6 h-24 border-l-2 border-gray-300"></div>
          <div class="block sm:hidden w-full h-0.5 bg-gray-300 my-4"></div>
          <!-- Right: Bar Chart -->
          <div class="relative flex-1 flex justify-center items-center w-full">
            <!-- Horizontal Bar Chart -->
            <div class="flex flex-col space-y-4 sm:space-y-6 z-10 w-full h-24 justify-center max-w-md">
              <div class="flex items-center space-x-3">
                <span class="text-xs text-slate-700 font-semibold min-w-[80px] drop-shadow-sm">Out of School</span>
                <div class="flex-1">
                  <div class="bg-purple-400 h-8 rounded-l rounded-r-md" style="width: 80%;"></div>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <span class="text-xs text-slate-700 font-semibold min-w-[80px] drop-shadow-sm">In School</span>
                <div class="flex-1">
                  <div class="bg-gray-300 h-8 rounded-l rounded-r-md" style="width: 20%;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <p>
          High-quality afterschool STEM programs have been proven to boost:
          <sup id="fnref5"><a href="#fn5" class="text-purple-700">[5]</a></sup>
        </p>

        <!-- STEM Impact Cards with Tailwind CSS -->
        <div class="flex flex-col sm:flex-row justify-center gap-6 w-full my-8">
          <!-- Card 1 -->
          <div class="bg-white rounded-xl border border-gray-300 shadow-sm flex flex-col items-center w-full sm:w-[270px] h-[300px] min-w-[0] max-w-full sm:min-w-[230px] sm:max-w-[320px] min-h-[200px] max-h-[280px] overflow-hidden mb-6 sm:mb-0">
            <img src="/public/kid_doing_reaction.jpeg" alt="STEM Interest and Identity" class="w-full h-28 object-cover">
            <div class="px-4 py-3 flex-1 flex flex-col justify-start items-center text-center">
              <h3 class="text-lg font-semibold mb-1 text-purple-950">STEM Interest and Identity</h3>
              <p class="text-slate-700 text-sm">
                Fostering a passion for science, technology, engineering, and math, and building a sense of belonging in STEM fields.
              </p>
            </div>
          </div>
          <!-- Card 2 -->
          <div class="bg-white rounded-xl border border-gray-300 shadow-sm flex flex-col items-center w-full sm:w-[270px] h-[300px] min-w-[0] max-w-full sm:min-w-[230px] sm:max-w-[320px] min-h-[200px] max-h-[280px] overflow-hidden mb-6 sm:mb-0">
            <img src="/public/enpeng_atoms_2.jpeg" alt="Career Awareness and Readiness" class="w-full h-28 object-cover">
            <div class="px-4 py-3 flex-1 flex flex-col justify-start items-center text-center">
              <h3 class="text-lg font-semibold mb-1 text-purple-950">Career Awareness and Readiness</h3>
              <p class="text-slate-700 text-sm">
                Developing awareness of STEM careers and building skills necessary for future academic and professional success.
              </p>
            </div>
          </div>
          <!-- Card 3 -->
          <div class="bg-white rounded-xl border border-gray-300 shadow-sm flex flex-col items-center w-full sm:w-[270px] h-[300px] min-w-[0] max-w-full sm:min-w-[230px] sm:max-w-[320px] min-h-[200px] max-h-[280px] overflow-hidden">
            <img src="/public/2_kids_holding_bridge.jpeg" alt="Problem-Solving and Perseverance" class="w-full h-28 object-cover">
            <div class="px-4 py-3 flex-1 flex flex-col justify-start items-center text-center">
              <h3 class="text-lg font-semibold mb-1 text-purple-950">Problem-Solving and Perseverance</h3>
              <p class="text-slate-700 text-sm">
                Encouraging creative problem-solving, resilience, and persistence in the face of challenges.
              </p>
            </div>
          </div>
        </div>

        <p class="font-semibold text-purple-950 text-xl mt-6">For example:</p>
        <ul class="list-disc text-left pl-6 sm:pl-8 max-w-md mx-auto">
          <li>Chicago’s Science Club saw STEM college majors jump from <strong>1% to 34%</strong>
            <a href="https://www.nsta.org/connected-science-learning/connected-science-learning-march-2016/science-club" class="text-purple-700 underline" id="fnref6">[6]</a>
          </li>
          <li>The Clubhouse Network helped <strong>80% of alumni</strong> enter STEM careers
            <a href="https://theclubhousenetwork.org/" class="text-purple-700 underline" id="fnref7">[7]</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="bg-white p-8 rounded-2xl border mt-8 border-gray-300 z-30 shadow-md transform flex flex-col items-center justify-center">
      <h3 class="text-3xl font-bold text-center text-purple-950 mb-4">We’re Working Towards Closing the Gap<sup id="fnref8"><a href="#fn8" class="text-purple-700">[8]</a></sup></h3>
      <div class="text-gray-500 max-w-2xl text-center space-y-4">
        <p>
          Wealthier families invest over <strong>3.5x more</strong> in out-of-school enrichment than lower-income families. This is how STEM access gaps widen.
        </p>
        <div class="flex items-center justify-center gap-12 my-8">
          <!-- 1× Icon Group -->
          <div class="flex flex-col items-center justify-center space-y-2">
            <div class="flex gap-1">
              <!-- 1 Full Dollar Icon -->
              <svg xmlns="http://www.w3.org/2000/svg" class="hover:scale-110 transform transition-all duration-300 w-12 h-12 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2 4h20v16H2z" opacity="0.2"/>
                <path d="M4 6h16v12H4z" class="fill-current"/>
                <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
              </svg>
            </div>
            <p class="text-s text-center max-w-[12.5rem] text-slate-700 font-semibold">
              Spent by lower-income families
            </p>
          </div>

          <!-- 3.5× Icon Group -->
          <div class="flex flex-col items-center justify-center space-y-2">
            <div class="flex gap-1">
              <!-- 3 Full Dollar Icons -->
              <svg xmlns="http://www.w3.org/2000/svg" class="hover:scale-110 transform transition-all duration-300 w-12 h-12 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2 4h20v16H2z" opacity="0.2"/>
                <path d="M4 6h16v12H4z" class="fill-current"/>
                <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" class="hover:scale-110 transform transition-all duration-300 w-12 h-12 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2 4h20v16H2z" opacity="0.2"/>
                <path d="M4 6h16v12H4z" class="fill-current"/>
                <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" class="hover:scale-110 transform transition-all duration-300 w-12 h-12 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2 4h20v16H2z" opacity="0.2"/>
                <path d="M4 6h16v12H4z" class="fill-current"/>
                <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
              </svg>
              <!-- 0.5 Dimmed Dollar Icon -->
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
                <!-- Left half: vivid dollar (purple-600) -->
                <g>
                  <clipPath id="leftHalf">
                    <rect x="0" y="0" width="24" height="48"/>
                  </clipPath>
                  <g clip-path="url(#leftHalf)">
                    <rect x="0" y="0" width="48" height="48" fill="none"/>
                    <g transform="scale(2)">
                      <path d="M2 4h20v16H2z" fill="currentColor" opacity="0.2"/>
                      <path d="M4 6h16v12H4z" fill="#9333ea"/>
                      <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="sans-serif">$</text>
                    </g>
                  </g>
                </g>
                <!-- Right half: dimmed dollar (purple-400, opacity-0.5) -->
                <g>
                  <clipPath id="rightHalf">
                    <rect x="24" y="0" width="24" height="48"/>
                  </clipPath>
                  <g clip-path="url(#rightHalf)" opacity="0.5">
                    <rect x="0" y="0" width="48" height="48" fill="none"/>
                    <g transform="scale(2)">
                      <path d="M2 4h20v16H2z" fill="currentColor" opacity="0.2"/>
                      <path d="M4 6h16v12H4z" fill="#a78bfa"/>
                      <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="sans-serif">$</text>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
            <p class="text-s text-center max-w-[12.5rem] text-slate-700 font-semibold">
              Spent by higher-income families
            </p>
          </div>
        </div>
        <h2 class="font-semibold text-purple-950 text-xl">
          RISE is tackling this divide by making engaging, hands-on STEM learning accessible to everyone.
        </h2>
        <!-- RISE Impact Section - Full Width Fix -->
        <div class="w-full mx-auto flex flex-col md:flex-row items-center justify-center">
          <!-- Left: Placeholder Image -->
          <div class="md:w-2/5 w-full h-full flex rounded-lg items-center justify-center ">
            <img
                    src="/public/enpeng_holding_bucket.jpeg"
                    alt="STEM Learning"
                    class="object-cover w-full h-full rounded-2xl min-h-[350px] min-w-[300px]"
            >
          </div>
          <!-- Right: Content -->
          <div class="md:w-3/5 w-full text-gray-500 px-8 pl-8 py-6 flex flex-col justify-center">

            <ul class="mb-8 list-disc pl-6">
              <li class=" mb-5">Helping kids discover their passion for STEM</li>
              <li class=" mb-5">Making STEM exciting and easy to understand</li>
              <li class="">Building real-world skills that lead to college and careers</li>
            </ul>
            <p class="text-gray-500">
              Let’s stop letting potential go untapped. Let’s build a Georgia where <strong>every kid sees themselves in STEM</strong>.
            </p>
          </div>
        </div>
        <button
                onclick="window.location.href='/what-we-do';"
                class="px-8 py-4 mb-4 text-white font-bold bg-purple-950 rounded-full transform transition duration-300 hover:scale-110 hover:text-white w-max"
        >
          Continue Reading About Our Work
        </button>
      </div>
    </div>

    <!-- Footnotes Section -->
    <div class="bg-white p-8 rounded-2xl border mt-8 border-gray-300 z-30 shadow-md flex flex-col items-center justify-center">
      <h4 class="text-xl font-semibold text-purple-800 mb-2 -mt-2">Sources</h4>
      <ol class="list-decimal pl-6 text-sm text-gray-500 text-left">
        <li id="fn1">U.S. Bureau of Labor Statistics. <em>STEM Occupations Employment Projections</em>. <a href="https://www.bls.gov/emp/tables/stem-employment.htm" class="underline" target="_blank" rel="noopener">bls.gov</a> <a href="#fnref1" title="Jump back to content">↩</a></li>
        <li id="fn2">Georgia Department of Labor. <em>Hot STEM Jobs Report</em>. <a href="https://explorer.gdol.ga.gov/vosnet/mis/current/stem.pdf" class="underline" target="_blank" rel="noopener">gdol.ga.gov</a> <a href="#fnref2" title="Jump back to content">↩</a></li>
        <li id="fn3">Georgia Department of Education. <em>Georgia Milestones Data Dashboard</em>, 2024. <a href="https://georgiainsights.gadoe.org/Dashboards/Pages/Georgia-Milestones.aspx" class="underline" target="_blank" rel="noopener">georgiainsights.gadoe.org</a> <a href="#fnref3" title="Jump back to content">↩</a></li>
        <li id="fn4">Banks et al. <em>Diversity in Learning Outside of School</em>. <a href="http://life-slc.org/docs/Banks_etal-LIFE-Diversity-Report.pdf" class="underline" target="_blank" rel="noopener">life-slc.org</a> <a href="#fnref4" title="Jump back to content">↩</a></li>
        <li id="fn5">Afterschool Alliance. <em>Students Learn More with Afterschool STEM</em>. April 2018. <a href="https://docs.wixstatic.com/ugd/e45463_e14ee6fac98d405e950c66fe28de9bf8.pdf" class="underline" target="_blank" rel="noopener">PDF</a> <a href="#fnref5" title="Jump back to content">↩</a></li>
        <li id="fn6">NSTA. <em>Connected Science Learning – Science Club</em>. <a href="https://www.nsta.org/connected-science-learning/connected-science-learning-march-2016/science-club" class="underline" target="_blank" rel="noopener">nsta.org</a> <a href="#fnref6" title="Jump back to content">↩</a></li>
        <li id="fn7">The Clubhouse Network. <a href="https://theclubhousenetwork.org/" class="underline" target="_blank" rel="noopener">theclubhousenetwork.org</a> <a href="#fnref7" title="Jump back to content">↩</a></li>
        <li id="fn8">American Sociological Association. <em>Income Inequality is Changing How Parents Invest in Their Kids</em>. <a href="https://www.asanet.org/news_item/income-inequality-changing-how-parents-invest-their-kids-widening-class-divides-us/?utm_source=chatgpt.com" class="underline" target="_blank" rel="noopener">asanet.org</a> <a href="#fnref8" title="Jump back to content">↩</a></li>
      </ol>
    </div>

  </section>

  <!-- Footer -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>  
</main>

<!-- Contact Modal (hidden by default) -->
<div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
    <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
    <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
    <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
      <div>
        <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
      </div>
      <div>
        <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
        <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
      </div>
      <div class="flex justify-center">
        <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
      </div>
    </form>
  </div>
</div>

<script>
  function logDebug(msg) {
    if (window && window.console) {
      console.debug('[DEBUG]', msg);
    }
  }

  // --- Contact Modal Setup (GLOBAL) ---
  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    // Find all "Contact Us" buttons/links in header
    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
    logDebug("setupContactModal run. Found " + openContactBtns.length + " openers.");
  }

  // --- Count Up Animation ---
  function animateCountUp(element, target, duration) {
    let start = 0;
    let startTime = null;

    function update(currentTime) {
      if (!startTime) startTime = currentTime;
      const progress = currentTime - startTime;
      const percent = Math.min(progress / duration, 1);

      element.textContent = Math.floor(percent * target) + "%";

      if (progress < duration) {
        requestAnimationFrame(update);
      } else {
        element.textContent = target + "%";
      }
    }

    requestAnimationFrame(update);
  }

  function setupCounterAnimation() {
    const counterSection = document.querySelector('#Issue');
    if (counterSection) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const counter = document.getElementById('counter');
            if (counter) {
              animateCountUp(counter, 51, 2300);
            } else {
              logDebug("Counter element not found in #Issue");
            }
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.3 });
      observer.observe(counterSection);
    } else {
      logDebug("#Issue section not found, skipping counter animation");
    }
  }

  // --- Main Init ---
  document.addEventListener('DOMContentLoaded', function() {
    // Hamburger menu functionality (if present)
    const hamburgerBtn = document.getElementById("hamburger-btn");
    if (hamburgerBtn) {
      hamburgerBtn.addEventListener("click", function() {
        const navbar = document.getElementById("navbar-sticky");
        if (!navbar) {
          logDebug("Navbar sticky not found!");
          return;
        }
        const isExpanded = this.getAttribute("aria-expanded") === "true";
        navbar.classList.toggle("hidden", isExpanded);
        this.setAttribute("aria-expanded", !isExpanded);
      });
    } else {
      logDebug("Hamburger button not found, skipping menu setup");
    }

    setupCounterAnimation();

    // Set up modal for the first time (in case a Contact Us button is present early)
    setupContactModal();
  });

  // Listen for custom header-loaded event (from loadHeader.js) to re-setup modal openers
  document.addEventListener('header:loaded', () => {
    setupContactModal();
  });
</script>

</body>
</html>

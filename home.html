<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <link rel="icon" href="/public/favicon.ico"/>
    <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
    <link rel="shortcut icon" href="/public/favicon.ico">
    <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RISE STEM – Atlanta’s Youth STEM Nonprofit</title>
    <link href="/src/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/src/animations.css">
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
   <!-- Header -->
  <div id="header-container"></div>
  <script type="module" src="/scripts/loadHeader.js"></script>
  <!-- Main Content Section Below Header -->
 <section id="home" class="min-h-screen bg-gray-50 w-full relative overflow-hidden">
  <div 
    x-data="{
      images: [
        { url: '/public/enpeng_happy.jpeg', position: 'bg-top' },
        { url: '/public/group_waiting_to_test.jpeg', position: 'bg-[center_40%]' },
        { url: '/public/lab.jpeg', position: 'bg-top' }
      ],
      currentIndex: 0,
      previousIndex: 0,
      direction: 1,
      animating: false,
      interval: null,
      setIndex(newIdx, dir = 1) {
        if (this.animating || newIdx === this.currentIndex) return;
        this.direction = dir;
        this.previousIndex = this.currentIndex;
        this.currentIndex = newIdx;
        this.animating = true;
        setTimeout(() => { this.animating = false }, 700);
      },
      next() {
        this.setIndex((this.currentIndex + 1) % this.images.length, 1);
      },
      prev() {
        this.setIndex((this.currentIndex - 1 + this.images.length) % this.images.length, -1);
      },
      autoRotate() {
        this.interval = setInterval(() => this.next(), 10000);
      },
      stopRotate() {
        clearInterval(this.interval);
      }
    }"
    x-init="
      autoRotate();
      images.forEach(img => { const i = new Image(); i.src = img.url; });
    "
    @mouseenter="stopRotate()" @mouseleave="autoRotate()"
    class="w-full h-[80vh] relative overflow-hidden"
  >

    <!-- Slides -->
    <div class="absolute inset-0 z-0 w-full h-full overflow-hidden">
      <template x-for="(image, index) in images" :key="index">
        <div 
          class="absolute inset-0 w-full h-full bg-cover bg-no-repeat transition-all duration-700 ease-in-out"
          :class="[
            index === currentIndex 
              ? 'translate-x-0 z-30 opacity-100' 
              : (index === previousIndex && direction === 1)
                ? '-translate-x-full z-20 opacity-100'
                : (index === previousIndex && direction === -1)
                  ? 'translate-x-full z-20 opacity-100'
                  : 'translate-x-full z-10 opacity-0',
            image.position
          ]"
          :style="`background-image: url(${image.url});`"
        ></div>
      </template>
    </div>

    <!-- Overlay text -->
    <div class="absolute inset-0 z-40 flex flex-col justify-center items-center text-center px-6 sm:px-16 md:px-32 h-full pointer-events-none">
      <h1 class="fadeInUp-animation font-poppins text-3xl font-extrabold pt-8 text-white md:text-4xl xl:text-5xl drop-shadow-lg">
        RISE STEM
      </h1>
      <p class="fadeInUp-animation text-white font-semibold md:text-xl lg:text-2xl mt-4 drop-shadow-lg">
        Inspiring the next generation of <br>
        innovators, problem solvers, <br>
        creators, and critical thinkers.
      </p>
    </div>

    <!-- Overlay background -->
    <div class="absolute inset-0 bg-black opacity-40 z-30 pointer-events-none"></div>

    <!-- Dot Indicators -->
    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-50 mb-5 pointer-events-none">
      <template x-for="(image, index) in images" :key="index">
        <div
          class="w-3 h-3 rounded-full"
          :class="currentIndex === index ? 'bg-white' : 'bg-white/50'"
        ></div>
      </template>
    </div>
  </div>

<!-- Welcome Content Box (single instance, deduped) -->
<div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 -mt-8 z-30 relative pb-8">
  <div class="bg-white fadeInUp-animation p-4 rounded-2xl border border-gray-300 shadow-md justify-center">
    <div class="flex justify-center text-center">
      <h2 class="fadeInUp-animation text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
        Welcome to <span class="font-extrabold">RISE</span>
      </h2>
    </div>

    <div class="flex justify-center items-center pt-8 sm:px-16 lg:px-32 flex-col">
      <p class="fadeInUp-animation text-md text-gray-500 leading-relaxed text-center sm:px-4 md:px-8 lg:px-16 xl:px-32">
        RISE (Reinventing, Innovation, STEM, and Education) is a student-run organization aiming to empower students with free access to STEM education.
        Through hands-on projects and mentorship, we aim to inspire the next generation of engineers and coders.
      </p>
    </div>
  </div>

  <div class="bg-white mt-12  fadeInUp-animation p-4 rounded-2xl border border-gray-300 shadow-md justify-center">
    <div class="flex justify-center text-center">
      <h2 class="fadeInUp-animation text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
        RISE in <span class="font-extrabold">Action</span>
      </h2>
    </div>

        <style>
          .video-wrapper {
            padding: 1rem; /* Add minimum padding around the video */
          }
        
          .video-container {
            aspect-ratio: 16 / 9;
            width: 100%;
            max-width: 640px;  /* Optional: cap width on larger screens */
            margin: auto;
          }
        
          .video-container iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
          }
        </style>
        
        <div class="video-wrapper">
          <div class="video-container fadeInUp-animation">
            <iframe src="https://www.youtube.com/embed/mXId29EnPbg" frameborder="0" 
                    allow="autoplay" allowfullscreen>
            </iframe>
          </div>
        </div>
  </div>

      <div class="pt-12 w-full flex justify-center z-30 ">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-full h-128">
          <!-- Box 1 -->
          <div  class="fadeInUp-animation overflow-hidden rounded-lg border border-gray-300 bg-white z-30  shadow-lg">
            <!-- Background image -->
            <div class="fadeInUp-animation w-full bg-cover bg-center" style="background-image: url('/public/2_girls_gluing_bridge.jpeg'); height: 200px;"></div>
            <div class="fadeInUp-animation flex flex-col justify-center items-center p-4 space-y-4">
              <span class="text-xl font-bold text-purple-950 sm:text-2xl">The Need</span>
              <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                Early exposure to STEM is crucial for developing critical thinking and creativity. However, students from low-income backgrounds often miss out on these opportunities, limiting their potential to pursue STEM careers.
              </div>
              <a href="/issue" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                Learn More
              </a>
            </div>
            <script src="src/script.js"></script>
          </div>

          <!-- Box 2 -->
          <div data-delay="0.2" class="fadeInUp-animation overflow-hidden rounded-lg border border-gray-300 z-30  bg-white shadow-lg">
            <!-- Background image -->
            <div class="fadeInUp-animation w-full bg-cover bg-center" style="background-image: url('/public/owen_teaching.jpeg'); height: 200px;"></div>
            <div class="fadeInUp-animation flex flex-col justify-center items-center p-4 space-y-4">
              <span class="text-xl font-bold text-purple-950 sm:text-2xl">What We Do</span>
              <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                At RISE, we provide STEM education and mentorship to underserved communities, ensuring that all students, regardless of their background, have the tools they need to succeed in STEM fields.
              </div>
              <a href="/what-we-do" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                See How
              </a>
            </div>
          </div>

          <!-- Box 3 -->
          <div data-delay="0.4" class="fadeInUp-animation overflow-hidden rounded-lg border border-gray-300 z-30 bg-white shadow-lg">
            <!-- Background image -->
            <div class="fadeInUp-animation w-full bg-cover bg-center" style="background-image: url('/public/brayden_helping.jpeg'); height: 200px;"></div>
            <div class="fadeInUp-animation flex flex-col justify-center items-center p-4 space-y-4">
              <span class="text-xl font-bold text-purple-950 sm:text-2xl">Who We Are</span>
              <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                Through our programs, we've empowered countless students to explore STEM, fostering future scientists, engineers, and innovators. Together, we can close the opportunity gap and create a more equitable future.
              </div>
              <a href="/about" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                Read On
              </a>
            </div>
        </div>
      </div>
        </div>
      </div>
    </div>

    <div class="w-full fadeInUp-animation px-6 sm:px-6 md:px-16 lg:px-24 -mt-8 z-30 relative pb-8 pt-12">
      <!-- Rounded Box with content that will change -->
      <div class="bg-white z-30  p-4 rounded-2xl border border-gray-300 shadow-md justify-center">
        <!-- Latest News Carousel -->
        <div id="latest-news-carousel" class="fadeInUp-animation mt-0 mb-8 w-full flex flex-col items-center relative">
          <span class="text-3xl font-bold text-purple-950 sm:text-4xl mb-2">Latest From RISE</span>
          <div class="text-gray-700 text-center mb-4">Check out what RISE has been up to lately!</div>
          <a href="news_overview" class="text-purple-700 underline text-sm mb-2 hover:text-purple-900 transition">See all</a>
          <div class="relative w-full overflow-x-auto scrollbar-hide px-6 sm:px-6 md:px-16 lg:px-24" id="news-cards-scroll-container" style="padding-bottom: 0.5rem;">
            <div id="news-cards-container" class="flex flex-row gap-6 items-stretch">
              <!-- News cards go here -->
            </div>
          </div>
        </div>
        <style>
          .news-card {
            flex: 0 0 90vw;
            max-width: 22rem;
            min-width: 18rem;
            height: 18rem;
            margin: 0;
            box-shadow: 0 4px 16px 0 rgba(80, 0, 120, 0.13);
            background: #fff;
            border: 1.5px solid #e9d5ff;
            border-radius: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2.5rem 1.5rem 2rem 1.5rem;
            transition: box-shadow 0.3s, transform 0.3s;
          }
          @media (min-width: 768px) {
            .news-card {
              flex: 0 0 30%;
              min-width: 20rem;
              max-width: 22rem;
            }
          }
          @media (min-width: 1200px) {
            .news-card {
              flex: 0 0 28%;
              min-width: 22rem;
              max-width: 24rem;
            }
          }
          .scrollbar-hide::-webkit-scrollbar { display: none; }
          .scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }
        </style>
        <script>
          async function loadLatestNewsCarousel() {
            let updates = [];
            try {
              const res = await fetch('updates.json');
              const data = await res.json();
              if (Array.isArray(data)) {
                updates = data;
              } else if (Array.isArray(data.updates)) {
                updates = data.updates;
              }
            } catch {
              document.getElementById('latest-news-carousel').innerHTML += '<div class="text-red-600 mt-2">Could not load news updates.</div>';
              return;
            }
            if (!updates.length) return;
            // Show newest to oldest, left to right
            const sorted = updates; // No .reverse(), updates.json is already newest first
            const container = document.getElementById('news-cards-container');
            // Fetch titles from markdown files
            const titles = await Promise.all(sorted.map(async (u) => {
              try {
                const res = await fetch(u.file);
                const text = await res.text();
                const match = text.match(/^# (.+)$/m);
                return match ? match[1].trim() : u.file.split('/').pop().replace('.md','');
              } catch {
                return u.file.split('/').pop().replace('.md','');
              }
            }));
            // Fetch titles and images from markdown files
    const articlesData = await Promise.all(sorted.map(async (u) => {
      try {
        const res = await fetch(u.file);
        const text = await res.text();
        const titleMatch = text.match(/^# (.+)$/m);
        const title = titleMatch ? titleMatch[1].trim() : u.file.split('/').pop().replace('.md','');
        // Look for images in both markdown format and HTML format
        let imageMatch = text.match(/!\[.*?\]\((.*?)\)/);
        let image = imageMatch ? imageMatch[1] : null;
        if (!image) {
          imageMatch = text.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/i);
          image = imageMatch ? imageMatch[1] : null;
        }
        return { title, image };
      } catch {
        return {
          title: u.file.split('/').pop().replace('.md',''),
          image: null
        };
      }
    }));
    container.innerHTML = sorted.map((u, i) => `
      <div class="news-card w-full bg-white border-2 border-purple-100 rounded-2xl shadow-lg max-w-2xl mx-auto overflow-hidden mb-6">
        <a href="news_updates.html?updates=${encodeURIComponent(u.file)}" class="block group">
          <div class="flex flex-col px-6 py-10 items-center justify-center">
            <div class="text-2xl font-bold text-purple-950 mb-2 group-hover:text-purple-700 transition-colors duration-300 cursor-pointer text-center">
              ${articlesData[i].title}
            </div>
            <div class="text-gray-500 text-sm text-center">${formatNewsDate(u.datetime)}</div>
            ${articlesData[i].image ? `
              <div class="w-full flex items-center justify-center mt-2 mb-2">
                <img src="${articlesData[i].image}" alt="${articlesData[i].title}" class="w-full max-w-md h-32 md:h-40 object-cover rounded-lg group-hover:opacity-90 transition-opacity duration-300 mx-auto" />
              </div>
            ` : ''}
          </div>
        </a>
      </div>
    `).join('');
          }
          function formatNewsDate(dt) {
            if (!dt) return '';
            const dateObj = new Date(dt);
            const months = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
            return `${months[dateObj.getMonth()]} ${dateObj.getDate()}, ${dateObj.getFullYear()}`;
          }
          loadLatestNewsCarousel();
        </script>
      </div>
    </div>
    <div id="contact" class="w-full bg-purple-950 text-white pt-6">
      <div class="fadeInUp-animation container max-w-md mx-auto  flex flex-col grid-rows-2 items-center">
        <svg xmlns="http://www.w3.org/2000/svg" height="40px" viewBox="0 -960 960 960" width="40px" fill="#ffffff"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm320-280L160-640v400h640v-400L480-440Zm0-80 320-200H160l320 200ZM160-640v-80 480-400Z"/></svg>
        <h3 class="text-3xl font-extrabold text-white">Contact Us</h3>
      </div>
      <div class="fadeInUp-animation w-full px-12 sm:px-12 md:px-16 lg:px-32 mx-auto pt-12 p-6 shadow-md text-gray-500">
        <form
                action="https://formspree.io/f/xwpvqjel"
                method="POST"
                class="space-y-4"
        >
          <!-- Email Input -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
            </label>
            <input
                    type="email"
                    name="email"
                    id="email"
                    required
                    class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                    placeholder="Enter your email"
            />
          </div>

          <!-- Message Input -->
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700">
            </label>
            <textarea
                    name="message"
                    id="message"
                    required
                    rows="4"
                    class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                    placeholder="Enter your message"
            ></textarea>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center">
            <button
                    type="submit"
                    class="w-full px-4 py-2 font-bold bg-white text-purple-950 rounded-md transition-all duration-200"
            >
              Send
            </button>
          </div>
        </form>
      </div>
    </div>
    <!-- Contact Modal (hidden by default) -->
    <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
      <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
      <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
      <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
        <div>
          <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
        </div>
        <div>
          <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
    document.addEventListener('header:loaded', function() {
      setupContactModal();
    });
  
    document.addEventListener('DOMContentLoaded', function() {
      setupContactModal();
    });
  
    function setupContactModal() {
      const contactModal = document.getElementById('contact-modal');
      const closeContactBtn = document.getElementById('close-contact-modal');
  
      const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
              el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
      );
  
      openContactBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
          e.preventDefault();
          if (contactModal) contactModal.classList.remove('hidden');
        });
      });
  
      if (closeContactBtn) {
        closeContactBtn.addEventListener('click', function() {
          contactModal.classList.add('hidden');
        });
      }
  
      if (contactModal) {
        contactModal.addEventListener('click', function(e) {
          if (e.target === contactModal) {
            contactModal.classList.add('hidden');
          }
        });
      }
    }
  </script>
          
    </script>
    <div>
      <!-- Footer Section -->
      <div id="footer-container"></div>
      <script type="module" src="/scripts/loadFooter.js"></script>
    </div>
  </section>
</html>

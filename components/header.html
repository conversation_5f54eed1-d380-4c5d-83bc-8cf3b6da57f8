<header class="fixed top-0 bg-white shadow-md w-full z-50">
  <nav class="bg-white fixed w-full z-20 top-0 start-0 border-b border-gray-200 shadow-md">
    <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4 min-h-[80px] transition-all duration-300">
      <a href="/home" class="flex items-center px-0">
        <img src="/public/logo1.svg" alt="Rise Engineering & Coding" class="h-16 mr-3" />
      </a>

      <div class="flex justify-center items-center md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
        <a href="">
          <button id="open-contact-modal" type="button" class="text-white bg-purple-950 font-semibold rounded-md text-md px-6 py-3.5 sm:mr-2 lg:mr-0 transform transition-all duration-300 hover:scale-110">
            Contact Us
          </button>
        </a>
        <button id="hamburger-btn" data-collapse-toggle="navbar-sticky" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200" aria-controls="navbar-sticky" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
          </svg>
        </button>
      </div>

      <div class="hidden w-full md:flex md:w-auto md:order-1 justify-center" id="navbar-sticky">
        <ul class="flex flex-col md:flex-row md:items-center p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:mt-0 md:border-0 md:bg-white w-full md:w-auto">
          <li class="flex items-center">
            <a href="/home" class="px-4 py-2 font-bold text-gray-500 hover:text-purple-950">Home</a>
          </li>
          <!-- Projects dropdown for desktop -->
          <li class="group hidden md:flex md:flex-col relative items-center">
            <a href="/projects" class="px-4 py-2 font-bold text-gray-500 hover:text-purple-950 cursor-pointer flex items-center group">
              Projects
              <svg class="w-4 h-4 ml-1 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M19 9l-7 7-7-7"></path>
              </svg>
            </a>
            <!-- Dropdown -->
            <div class="absolute left-0 top-full mt-0 py-1 bg-white shadow-lg rounded-b-lg z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out transform translate-y-2 group-hover:translate-y-0 min-w-[180px]">
              <a href="/curriculum" class="block px-4 py-3 font-bold text-gray-500 hover:text-purple-950 duration-200">
                Curriculum
              </a>
              <a href="/kits" class="block px-4 py-3 font-bold text-gray-500 hover:text-purple-950 duration-200">
                Kits
              </a>
              <a href="/workshops" class="block px-4 py-3 font-bold text-gray-500 hover:text-purple-950 duration-200">
                Workshops
              </a>
            </div>
          </li>
          <li class="flex items-center md:hidden">
            <!-- Projects links for mobile -->
            <a href="/projects" class="px-4 py-2 pt-3 font-bold hover:text-gray-500 text-gray-500">Projects</a>
            <ul class="pl-6 space-y-1 pt-1 text-gray-500">
              <li>
                <a href="/curriculum" class="block hover:text-purple-900">Curriculum</a>
              </li>
              <li>
                <a href="/kits" class="block hover:text-purple-900">Kits</a>
              </li>
              <li>
                <a href="/workshops" class="block hover:text-purple-900">Workshops</a>
              </li>
            </ul>
          </li>
          <li class="flex items-center">
            <a href="/questions" class="px-4 py-2 font-bold text-gray-500 hover:text-purple-950">Questions</a>
          </li>
          <li class="flex items-center">
            <a href="/issue" class="px-4 py-2 font-bold text-gray-500 hover:text-purple-950">The Issue</a>
          </li>
          <!-- About dropdown for desktop -->
          <li class="group hidden md:flex md:flex-col relative items-center">
            <a href="/about" class="px-4 py-2 font-bold text-gray-500 hover:text-purple-950 cursor-pointer flex items-center group">
              About Us
              <svg class="w-4 h-4 ml-1 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M19 9l-7 7-7-7"></path>
              </svg>
            </a>
            <!-- Dropdown -->
            <div class="absolute left-0 top-full mt-0 py-1 bg-white shadow-lg rounded-b-lg z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out transform translate-y-2 group-hover:translate-y-0 min-w-[180px]">
              <a href="/what-we-do" class="block px-4 py-3 font-bold text-gray-500 hover:text-purple-950 duration-200">
                Our Work
              </a>
              <a href="/team" class="block px-4 py-3 font-bold text-gray-500 hover:text-purple-950 duration-200">
                Our Team
              </a>
            </div>
          </li>

          <!-- About links for mobile -->
          <li class="block md:hidden">
            <a href="/about" class="px-4 py-2 pt-3 font-bold hover:text-gray-500 text-gray-500">About</a>
            <ul class="pl-6 space-y-1 pt-1 text-gray-500">
              <li>
                <a href="/what-we-do" class="block hover:text-purple-900">Our Work</a>
              </li>
              <li>
                <a href="/team" class="block hover:text-purple-900">Our Team</a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</header>
<style>
/* Completely remove any background effects from dropdown items */
.group .absolute a {
  background-color: transparent !important;
  background: transparent !important;
}
.group .absolute a:hover {
  background-color: transparent !important;
  background: transparent !important;
}
.group .absolute a:focus {
  background-color: transparent !important;
  background: transparent !important;
}
.group .absolute a:active {
  background-color: transparent !important;
  background: transparent !important;
}
</style>
<script>
  // Hamburger toggle
  document.getElementById("hamburger-btn").addEventListener("click", function () {
    const navbar = document.getElementById("navbar-sticky");
    const isExpanded = this.getAttribute("aria-expanded") === "true";
    navbar.classList.toggle("hidden", isExpanded);
    this.setAttribute("aria-expanded", !isExpanded);
  });

  // Highlight current page link
  document.addEventListener("DOMContentLoaded", () => {
    const currentURL = window.location.href;
    const navLinks = document.querySelectorAll('nav a');

    console.log("Current page URL:", currentURL);
    console.log("Checking navigation links...");

    navLinks.forEach(link => {
      console.log(`Checking link: ${link.href}`);
      
      if (link.href === currentURL) {
        console.log(`Match found! Highlighting link: ${link.href}`);
        link.classList.add('text-purple-950');
        link.classList.remove('text-gray-500');

        if (link.closest('li.group')) {
          console.log("Link is inside a dropdown. Highlighting parent link.");
          const aboutParent = document.querySelector('li.group > a');
          if (aboutParent) {
            aboutParent.classList.add('text-purple-950');
            aboutParent.classList.remove('text-gray-500');
          } else {
            console.warn("Could not find the dropdown parent anchor.");
          }
        }
      }
    });
  });
</script>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Login Required</title>
  <link rel="icon" href="/public/favicon.ico"/>
  <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
  <link rel="shortcut icon" href="/public/favicon.ico">
  <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700;900&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    function checkLogin() {
      const password = document.getElementById('password').value;
      if (password === 'rise2025') {
        window.location.href = 'admin.html';
      } else {
        document.getElementById('error').textContent = 'Incorrect password';
        document.getElementById('error').classList.remove('hidden');
        document.getElementById('password').value = '';
        document.getElementById('password').focus();
      }
    }
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('password').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') checkLogin();
      });
    });
  </script>
  <style>
    body { font-family: 'Poppins', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
  <div class="bg-white rounded-2xl shadow-lg border border-purple-200 max-w-sm w-full p-8 flex flex-col items-center font-poppins">
    <h2 class="text-2xl md:text-3xl font-extrabold text-purple-950 mb-2 text-center">Login</h2>
    <p class="text-purple-700 mb-6 text-center text-base">Enter the password to continue.</p>
    <input 
      type="password" 
      id="password" 
      placeholder="Password" 
      autocomplete="current-password"
      class="w-full px-4 py-2 mb-4 border border-purple-300 rounded focus:outline-none focus:ring-2 focus:ring-purple-600 text-lg font-poppins"
    />
    <button 
      onclick="checkLogin()"
      class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200 hover:bg-purple-800 hover:scale-105 text-lg"
    >
      Login
    </button>
    <div id="error" class="text-red-600 mt-3 text-center text-sm hidden"></div>
  </div>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>News Updates - RISE STEM</title>
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <link href="/src/style.css" rel="stylesheet" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
    rel="stylesheet"
  />
  <link rel="stylesheet" href="src/animations.css" />

  <style>
    html {
      scroll-behavior: smooth;
    }
    body {
      font-family: 'Poppins', sans-serif;
    }

    /* Breadcrumb styling */
    .breadcrumb-nav a {
      color: #581c87 !important;
      text-decoration: none !important;
      transition: all 0.2s ease-in-out;
    }
    .breadcrumb-nav a:hover {
      color: #7c3aed !important;
      text-decoration: underline !important;
    }
    .breadcrumb-nav span {
      color: #6b7280;
    }

    /* The white “markdown-body” box */
    .markdown-body {
      max-width: 800px;
      margin: 6rem auto 2rem auto; /* top margin 6rem so it sits below the header */
      padding: 2.5rem 2rem;
      background: #fff;
      border-radius: 1.5rem;
      box-shadow: 0 2px 12px rgba(80, 0, 120, 0.08);
      color: #23272f;
      text-align: left;
      font-size: 1.15rem;
      line-height: 1.7;
      border: 1.5px solid #e5e7eb;
      border-top: 3px solid #e5e7eb;
    }
    /* Markdown headings, lists, etc. */
    .markdown-body h1 {
      font-size: 2.5em;
      margin-top: 1.2em;
      margin-bottom: 0.6em;
      font-weight: 800;
      border-bottom: 1px solid #ede9fe;
      padding-bottom: 0.3em;
      color: #3c0366; /* Tailwind purple-50 */
    }
    .markdown-body h2 {
      font-size: 2em;
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: 700;
      border-bottom: 1px solid #ede9fe;
      padding-bottom: 0.2em;
    }
    .markdown-body h3 {
      font-size: 1.5em;
      margin-top: 0.9em;
      margin-bottom: 0.4em;
      font-weight: 600;
    }
    .markdown-body h4,
    .markdown-body h5,
    .markdown-body h6 {
      font-size: 1.2em;
      margin-top: 0.7em;
      margin-bottom: 0.3em;
      font-weight: 500;
    }
    .markdown-body strong,
    .markdown-body b {
      font-weight: 700;
    }
    .markdown-body em,
    .markdown-body i {
      font-style: italic;
    }
    .markdown-body ul,
    .markdown-body ol {
      margin: 1em 0 1em 2em;
      padding-left: 1.5em;
    }
    .markdown-body li {
      margin-bottom: 0.5em;
    }
    .markdown-body blockquote {
      border-left: 4px solid #a78bfa;
      background: #f3f4f6;
      color: #4b5563;
      margin: 1.5em 0;
      padding: 1em 2em;
      border-radius: 0.5em;
      font-style: italic;
      text-align: left;
    }
    .markdown-body pre,
    .markdown-body code {
      background: #f3f4f6;
      color: #23272f;
      border-radius: 0.4em;
      padding: 0.2em 0.5em;
      font-size: 1em;
      font-family: 'Fira Mono', 'Consolas', 'Monaco', monospace;
    }
    .markdown-body pre {
      padding: 1em;
      overflow-x: auto;
    }
    .markdown-body hr {
      border: none;
      border-top: 2px solid #ede9fe;
      margin: 2em auto;
      width: 60%;
    }
    .markdown-body a {
      color: #6d28d9;
      text-decoration: underline;
      word-break: break-word;
    }
    .markdown-body img {
      display: block;
      margin: 1.5em auto;
      max-width: 100%;
      border-radius: 0.75rem;
      box-shadow: 0 1px 6px rgba(80, 0, 120, 0.06);
    }
    .markdown-body p {
      color: #23272f;
      margin-bottom: 1em;
      text-align: left;
    }

    /* “Updated: …” date styling */
    .news-updated-date {
      font-size: 0.85em;
      color: #6b7280;
      margin-bottom: 0.5em;
      font-weight: 400;
      text-align: left;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
  <!-- Header -->
  <div id="header-container"></div>
  <script type="module" src="/scripts/loadHeader.js"></script>

  <!-- Push content down below the fixed header -->
  <div style="padding-top: 4rem;"></div>

  <!-- News Content Section -->
  <section id="home" class="bg-gray-50 w-full pb-12 flex-grow">
    <div id="news-content" class="markdown-body">
      <div class="breadcrumb-nav flex items-center text-lg font-normal">
        <a href="/rise/home">Home</a>
        <span class="mx-4">›</span>
        <a href="news_overview.html">News Overview</a>
        <span class="mx-4">›</span>
        <span id="breadcrumb-current" class="font-bold text-gray-800"></span>
      </div>
      <!-- Rendered markdown will appear here -->
      <div id="news-markdown"></div>
      <!-- Updated date (if available) -->
      <div id="news-updated-date" class="news-updated-date"></div>
    </div>
    <div id="news-error" class="text-red-600 text-center mt-4"></div>
  </section>

  <!-- Footer -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
  </footer>

  <!-- Contact Modal (hidden by default) -->
  <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
      <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
      <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
      <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
        <div>
          <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
        </div>
        <div>
          <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script>
    // Ensure DOM is loaded before attaching event listeners
    document.addEventListener("DOMContentLoaded", function() {
      // Hamburger menu toggle
      var hamburgerBtn = document.getElementById("hamburger-btn");
      if (hamburgerBtn) {
        hamburgerBtn.addEventListener("click", function () {
          const navbar = document.getElementById("navbar-sticky");
          const isExpanded = this.getAttribute("aria-expanded") === "true";
          if (navbar) {
            navbar.classList.toggle("hidden", isExpanded);
            this.setAttribute("aria-expanded", !isExpanded);
          }
        });
      }

      // Modal open/close logic for contact modal
      const contactModal = document.getElementById('contact-modal');
      const openContactBtn = document.querySelector('a[href="#contact"] button');
      const closeContactBtn = document.getElementById('close-contact-modal');

      if (openContactBtn && contactModal && closeContactBtn) {
        openContactBtn.addEventListener('click', function(e) {
          e.preventDefault();
          contactModal.classList.remove('hidden');
        });
        closeContactBtn.addEventListener('click', function() {
          contactModal.classList.add('hidden');
        });
        contactModal.addEventListener('click', function(e) {
          if (e.target === contactModal) {
            contactModal.classList.add('hidden');
          }
        });
      }
    });

    // Helper: get a query parameter by name
    function getQueryParam(name) {
      const url = new URL(window.location.href);
      return url.searchParams.get(name);
    }

    // Main: fetch markdown, extract title, render content
    async function loadNewsUpdate() {
      let mdFile = getQueryParam("updates");
      let updatesList = [];
      let latestUpdateObj = null;

      if (!mdFile) {
        // If no “updates” param, load the first entry from updates.json
        try {
          const res = await fetch("updates.json");
          updatesList = await res.json();
          if (updatesList.length > 0) {
            mdFile = updatesList[0].file;
          }
        } catch (e) {
          document.getElementById("news-error").textContent = "Could not load updates.json.";
          return;
        }
      } else {
        // If user specified mdFile in URL, still try to fetch updates.json for the date
        try {
          const res = await fetch("updates.json");
          updatesList = await res.json();
        } catch (e) {
          // proceed without date info if fetch fails
        }
      }

      // If we still don’t have mdFile, bail
      if (!mdFile) {
        document.getElementById("news-error").textContent = "No update file specified.";
        return;
      }

      // Fetch the markdown text
      try {
        const res = await fetch(mdFile);
        if (!res.ok) throw new Error("Not found");
        const md = await res.text();

        // 1) Extract the first-level heading (# ) as the “title”
        const headingMatch = md.match(/^#\s+(.+)$/m);
        const extractedTitle = headingMatch
          ? headingMatch[1].trim()
          : mdFile.split("/").pop().replace(".md", "");

        // 2) If updates.json gave us a datetime, format it for “Updated: …”
        let updatedDate = "";
        if (updatesList.length) {
          latestUpdateObj = updatesList.find(
            (u) => u.file === mdFile || u.file === "./" + mdFile
          );
          if (latestUpdateObj && latestUpdateObj.datetime) {
            const dateObj = new Date(latestUpdateObj.datetime);
            const months = [
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ];
            let hours = dateObj.getHours();
            const minutes = dateObj.getMinutes().toString().padStart(2, "0");
            const ampm = hours >= 12 ? "PM" : "AM";
            hours = hours % 12;
            hours = hours ? hours : 12; // 0 → 12
            updatedDate = `${months[dateObj.getMonth()]} ${dateObj.getDate()}, ${
              dateObj.getFullYear()
            }, ${hours}:${minutes} ${ampm}`;
          }
        }

        // 3) Populate “Updated: …” line (if we have one)
        if (updatedDate) {
          document.getElementById("news-updated-date").textContent = `Updated: ${updatedDate}`;
        }

        // 4) Populate breadcrumb current title
        document.getElementById("breadcrumb-current").textContent = extractedTitle;

        // 5) Render the markdown into the container
        document.getElementById("news-markdown").innerHTML = marked.parse(md);
      } catch (err) {
        document.getElementById("news-error").textContent = "Could not load this news update.";
      }
    }

    loadNewsUpdate();

    function setupContactModal() {
  const contactModal = document.getElementById('contact-modal');
  const closeContactBtn = document.getElementById('close-contact-modal');

  // Find all "Contact Us" buttons/links in header
  const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
    el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
  );

  openContactBtns.forEach(btn => {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      if (contactModal) contactModal.classList.remove('hidden');
    });
  });

  if (closeContactBtn) {
    closeContactBtn.addEventListener('click', function() {
      contactModal.classList.add('hidden');
    });
  }

  if (contactModal) {
    contactModal.addEventListener('click', function(e) {
      if (e.target === contactModal) {
        contactModal.classList.add('hidden');
      }
    });
  }
}
  </script>
</body>
</html>

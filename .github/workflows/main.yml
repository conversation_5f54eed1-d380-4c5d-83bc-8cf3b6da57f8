name: Update Latest News Link
on:
  push:
    paths:
      - 'news_updates/*.md'
      - '.github/workflows/update-latest.yml'  # Trigger if the workflow itself changes

jobs:
  update-latest:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history instead of just latest commit
          
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
          
      - name: Run script to regenerate updates.json
        run: |
          python scripts/generate_updates.py
          
      - name: Commit updates.json if it changed
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add updates.json
          if ! git diff --cached --quiet; then
            git commit -m 'Update latest update link'
            git push
          fi

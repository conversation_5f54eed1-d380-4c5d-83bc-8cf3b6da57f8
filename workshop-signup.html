<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Workshop Signup - RISE STEM</title>
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="/src/style.css" rel="stylesheet" />
    <link rel="stylesheet" href="/src/animations.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;700&display=swap" rel="stylesheet" />
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
    <style>
    /* Radio custom styles */
    input[type="radio"].custom-radio:checked::before {
      content: '';
      display: block;
      width: 10px;
      height: 10px;
      background-color: #5B21B6; /* Tailwind's purple-950 */
      border-radius: 9999px;
    }
    input[type="radio"].custom-radio {
      position: relative;
    }
    input[type="radio"].custom-radio::before {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    /* Checkbox custom styles */
    input[type="checkbox"].custom-checkbox {
      position: relative;
      appearance: none;
      width: 1rem;
      height: 1rem;
      border: 1.5px solid #D1D5DB; /* Tailwind's gray-300 */
      border-radius: 0.25rem;
      background: #fff;
      outline: none;
      transition: border 0.2s;
      cursor: pointer;
    }
    input[type="checkbox"].custom-checkbox:checked {
      border-color: #5B21B6; /* purple-950 when checked */
      background-color: #5B21B6;
    }
    input[type="checkbox"].custom-checkbox:checked::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 6px;
      height: 10px;
      border: solid #fff;
      border-width: 0 2px 2px 0;
      transform: translate(-50%, -65%) rotate(45deg);
      border-radius: 1px;
      box-sizing: border-box;
      pointer-events: none;
    }
    </style>
  </head>
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>
    <section class="bg-gray-50 w-full text-gray-800 relative overflow-hidden font-poppins">
      <!-- Main Content -->
      <section class="max-w-4xl mx-auto px-6 py-16 pt-32">
        <h1 class="text-4xl font-extrabold text-purple-950 mb-6 text-center">Workshop Signup</h1>
        <p class="text-center text-gray-500 text-sm mb-8">Join a free STEM workshop with RISE</p>
        <form id="signupForm" class="space-y-6">
          <div>
            <label class="block font-medium">Participant Name</label>
            <input type="text" name="participantName" required class="w-full px-4 py-2 border !outline-none focus:border-purple-950 bg-white text-gray-500 border-gray-300 rounded-md" />
          </div>
          <div>
            <label class="block font-medium">Age / Grade</label>
            <input type="text" name="ageGrade" required class="w-full px-4 py-2 border !outline-none focus:border-purple-950  bg-white text-gray-500 border-gray-300 rounded-md" />
          </div>
          <div>
            <label class="block font-medium">Parent/Guardian Name</label>
            <input type="text" name="guardianName" required class="w-full px-4 py-2 bg-white !outline-none focus:border-purple-950 text-gray-500 border border-gray-300 rounded-md" />
          </div>
          <div>
            <label class="block font-medium">Parent/Guardian Email</label>
            <input type="email" name="guardianEmail" required class="w-full px-4 py-2 bg-white !outline-none focus:border-purple-950 text-gray-500 border border-gray-300 rounded-md" />
          </div>

          <label class="flex items-center">
            <input type="radio" name="workshop" value="Intro to Coding" required
              class="custom-radio appearance-none w-4 h-4 rounded-full border border-gray-300 bg-white checked:border-purple-950 mr-2" />
            Intro to Coding, July 31 (Slots left: <span id="codingCount">?</span>)
          </label>
          <label class="flex items-center">
            <input type="radio" name="workshop" value="Robotics Basics" required
              class="custom-radio appearance-none w-4 h-4  rounded-full border border-gray-300 bg-white checked:border-purple-950 mr-2" />
            Robotics Basics, July 31 (Slots left: <span id="roboticsCount">?</span>)
          </label>
          <label class="flex items-center">
            <input type="radio" name="workshop" value="Creative Tech" required
              class="custom-radio appearance-none w-4 h-4 rounded-full border border-gray-300 bg-white checked:border-purple-950 mr-2" />
            Creative Tech, August 1 (Slots left: <span id="creativeCount">?</span>)
          </label>
          <div>
            <label class="block font-medium">Special Needs / Notes</label>
            <textarea name="notes" rows="3" class="w-full px-4 py-2 border border-gray-300 !outline-none focus:border-purple-950 rounded-md bg-white text-gray-500"></textarea>
          </div>
          <div class="flex items-start">
            <input type="checkbox" id="consent" name="consent" required class="custom-checkbox mr-2 mt-1" />
            <label for="consent">I give permission for my child to participate in RISE workshops and to be contacted regarding updates.</label>
          </div>
          <button type="submit" class="w-full py-3 bg-purple-950 text-white font-bold rounded-md hover:bg-purple-900">Submit</button>
        </form>
        <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
          <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
            <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
            <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
            <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
              <div>
                <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
              </div>
              <div>
                <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
                <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
              </div>
              <div class="flex justify-center">
                <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
              </div>
            </form>
          </div>
        </div>
      </section>
      <div id="footer-container"></div>
      <script type="module" src="/scripts/loadFooter.js"></script>
      <script>
        const scriptURL = 'https://script.google.com/macros/s/AKfycbxSjKtzK98WitYwgcZXOwbV1UoK0YrCh51SgVR_-jWZ_HOdd1f6nZrOEiZvg_TpvJKJ/exec';
        const workshopLimits = {
          "Intro to Coding": 5,
          "Robotics Basics": 3,
          "Creative Tech": 4
        };

        // Fetch remaining slots from Google Apps Script and update display
        function updateCountsDisplay() {
          fetch(scriptURL)
            .then(res => res.json())
            .then(remaining => {
              document.getElementById('codingCount').textContent = remaining["Intro to Coding"] ?? '?';
              document.getElementById('roboticsCount').textContent = remaining["Robotics Basics"] ?? '?';
              document.getElementById('creativeCount').textContent = remaining["Creative Tech"] ?? '?';
            });
        }

        updateCountsDisplay();

        document.getElementById('signupForm').addEventListener('submit', function(e) {
          e.preventDefault();
          fetch(scriptURL)
            .then(res => res.json())
            .then(remaining => {
              const data = Object.fromEntries(new FormData(this));
              const selectedWorkshop = data.workshop;
              if (!remaining[selectedWorkshop] || remaining[selectedWorkshop] <= 0) {
                alert("Sorry, " + selectedWorkshop + " is full.");
                updateCountsDisplay();
                return;
              }
              // Send signup to Google Apps Script
              fetch(scriptURL, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
              })
              .then(res => res.text())
              .then(() => {
                alert("Signup successful for " + selectedWorkshop + "!");
                this.reset();
                updateCountsDisplay();
              })
              .catch(() => alert("There was an error. Please try again."));
            });
        });
      </script>
    </section>
</html>

<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>News - RISE STEM</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html {
      scroll-behavior: smooth;
    }
    .section-content {
      display: none;
    }
    .section-content.active {
      display: block;
    }
    .tab-button.active {
      background-color: #4a1d96;
      color: white;
    }
  </style>
</head>
<body class="min-h-screen flex flex-col">
  <!-- Header/Navigation -->
<div id="header-container"></div>
<script type="module" src="/scripts/loadHeader.js"></script>
  
<main class="flex-1 bg-gray-50 w-full pt-32 pb-12">
  <div class="max-w-screen-xl mx-auto px-6 sm:px-6 md:px-16 lg:px-24">
    <h1 class="text-4xl font-extrabold text-purple-950 mb-6">All News Updates</h1>
    <div id="news-list-container" class="flex flex-col gap-8 w-full"></div>
  </div>
</main>

  <!-- Footer Section -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
</footer>

<script>
document.addEventListener('header:loaded', function() {
  setupContactModal();
});

// Optionally, run once on DOMContentLoaded for any "Contact Us" links/buttons already present
document.addEventListener('DOMContentLoaded', function() {
  setupContactModal();
});
</script>

  <script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>
  
<script>
  
async function loadAllNews() {
  let updates = [];
  try {
    const res = await fetch('updates.json');
    const data = await res.json();
    if (Array.isArray(data)) {
      updates = data;
    } else if (Array.isArray(data.updates)) {
      updates = data.updates;
    }
  } catch {
    document.getElementById('news-list-container').innerHTML = '<div class="text-red-600 mt-2">Could not load news updates.</div>';
    return;
  }
  if (!updates.length) return;
  
  // Fetch titles and images from markdown files
  const articlesData = await Promise.all(updates.map(async (u) => {
    try {
      const res = await fetch(u.file);
      const text = await res.text();
      const titleMatch = text.match(/^# (.+)$/m);
      const title = titleMatch ? titleMatch[1].trim() : u.file.split('/').pop().replace('.md','');
      
      // Look for images in both markdown format and HTML format
      // First try markdown format: ![alt](url)
      let imageMatch = text.match(/!\[.*?\]\((.*?)\)/);
      let image = imageMatch ? imageMatch[1] : null;
      
      // If no markdown image found, try HTML img tag: <img ... src="url" ...>
      if (!image) {
        imageMatch = text.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/i);
        image = imageMatch ? imageMatch[1] : null;
      }
      
      return { title, image };
    } catch {
      return { 
        title: u.file.split('/').pop().replace('.md',''), 
        image: null 
      };
    }
  }));
  
  const container = document.getElementById('news-list-container');
  container.innerHTML = updates.map((u, i) => `
    <div class="w-full bg-white border-2 border-purple-100 rounded-2xl shadow-lg max-w-6xl mx-auto overflow-hidden">
      <a href="news_updates.html?updates=${encodeURIComponent(u.file)}" class="block group">
        <div class="flex flex-col md:flex-row">
          <div class="flex-1 px-10 py-16">
            <div class="text-4xl font-bold text-purple-950 mb-4 group-hover:text-purple-700 transition-colors duration-300 cursor-pointer">${articlesData[i].title}</div>
            <div class="text-gray-500 text-lg">${formatNewsDate(u.datetime)}</div>
          </div>
          ${articlesData[i].image ? `
            <div class="flex-shrink-0 w-full md:w-80 p-4 md:py-6 md:pr-10 md:pl-0">
              <img src="${articlesData[i].image}" alt="${articlesData[i].title}" class="w-full h-36 md:h-56 object-cover rounded-lg group-hover:opacity-90 transition-opacity duration-300" />
            </div>
          ` : ''}
        </div>
      </a>
    </div>
  `).join('');
}

function formatNewsDate(dt) {
  if (!dt) return '';
  const dateObj = new Date(dt);
  const months = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
  return `${months[dateObj.getMonth()]} ${dateObj.getDate()}, ${dateObj.getFullYear()}`;
}

loadAllNews();
</script>

</body>
</html>

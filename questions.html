<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Questions - RISE STEM</title>
    <link href="src/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="src/animations.css">
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
  <div id="header-container"></div>
  <script type="module" src="scripts/loadHeader.js"></script>
<!-- Main content - About Us section -->
<section class="min-h-screen bg-white w-full ">
  <section class=" w-full px-8">
    <div class="px-16 sm:px-8 w-full h-[60vh] bg-purple-950 rounded-b-3xl flex items-center justify-center relative">
      <!-- Gradient Overlay Removed -->
      <div class="flex flex-col text-center items-center justify-center px-6 sm:px-16 md:px-32 z-10">
        <h1 class="mt-16 text-3xl font-extrabold text-white md:text-4xl xl:text-5xl justify-center">
          Frequently Asked Questions
        </h1>
      </div>
    </div>
  </section>

  <div id="faq" class="bg-white pt-6 sm:pt-6 md:pt-8 lg:pt-10"></div>
  <div class="bg-gray-100 pb-8 sm:pb-8 md:pb-12 lg:pb-16">
    <div class="container mx-auto flex flex-col items-center justify-center pt-8">
      <div class="w-full mx-auto px-12 sm:px-6 md:px-20 lg:px-32">
        <div class="space-y-2">
          <!-- Question 1 -->
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between hover:outline-gray-400 items-center focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(1)">
              <span class="font-bold text-gray-500">What is RISE Engineering & Coding?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-1">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-1">
              We are three students from The Westminster Schools of Atlanta who enjoy participating in community service work and wanted to give back to our community in a way that was connected to our passion in STEM. Our outreach program seeks to teach basic coding or engineering skills to middle school students across Atlanta.
            </div>
          </div>
          <!-- Question 2 -->
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between items-center focus:ring-0 focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(2)">
              <span class="font-bold text-gray-500">What does this program cost?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-2">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-2">
              Our goal is to provide STEM education to students for free or with minimal costs to the school. Our engineering curriculum requires materials other than a classroom with a projector, while our coding curriculum requires each student be able to access a computer (Chromebooks are fine).
            </div>
          </div>
          <!-- Question 3 -->
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between items-center focus:ring-0 focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(3)">
              <span class="font-bold text-gray-500">When are we available?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-3">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-3">
              Unfortunately due to conflicts with school hours, we can only come teach after-school after 3:45 (exact timing depends on commuting time between schools). The best days for us to come are Mondays, Wednesdays, or Fridays. Please reach out to us if you have more scheduling questions or concerns.
            </div>
          </div>
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between items-center focus:ring-0 focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(4)">
              <span class="font-bold text-gray-500">What age is our curriculum intended for?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-4">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-4">
              Our curriculum and projects are aimed at students in Middle School. Specifically grades 7 & 8 but can be slightly modified to include 6th graders.
            </div>
          </div>
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between items-center focus:ring-0 focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(5)">
              <span class="font-bold text-gray-500">Do students need prior experience?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-5">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-5">
              No prior experience is required. Our program is designed for all skill levels.
            </div>
          </div>
          <div class="border-b border-gray-300 max-w-full">
            <button class="w-full text-left p-4 flex justify-between items-center focus:ring-0 focus:outline-none hover:bg-gray-100 transition-all duration-200 max-w-full" onclick="toggleFAQ(6)">
              <span class="font-bold text-gray-500">What if I still have a question?</span>
              <span class="transition-transform transform rotate-0 text-gray-500" id="icon-6">▼</span>
            </button>
            <div class="hidden p-4 text-gray-500" id="answer-6">
              If you still have more questions, you can email <NAME_EMAIL>.
            </div>
          </div>
        </div>
      </div>

      <script>
        function toggleFAQ(num) {
          const answer = document.getElementById("answer-" + num);
          const icon = document.getElementById("icon-" + num);

          answer.classList.toggle("hidden");
          icon.classList.toggle("rotate-180");
        }
      </script>
    </div>
  </div>
  <section class="px-12 sm:px-6 pt-12 bg-white">
    <div id="contact" class="w-full mx-auto bg-white border border-gray-300 rounded-2xl shadow-lg py-6 px-10">
      <div class="flex flex-col items-center">
        <h3 class="text-3xl font-bold text-gray-500 mt-4">Still Have Questions?</h3>
        <p class="text-gray-500 text-center mt-2">We’d love to hear from you! Send us a message and we’ll get back to you soon.</p>
      </div>
      <div class="w-full mt-4">
        <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-6">
          <!-- Email Input -->
          <div>
            <label for="email" class="block text-sm font-semibold py-2 text-gray-500">Email Address</label>
            <input
                    type="email"
                    name="email"
                    id="email"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-gray-50"
                    placeholder="<EMAIL>"
            />
          </div>

          <!-- Message Input -->
          <div>
            <label for="message" class="block text-sm font-semibold py-2 text-gray-500">Your Message</label>
            <textarea
                    name="message"
                    id="message"
                    required
                    rows="5"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-gray-50"
                    placeholder="Type your message here..."
            ></textarea>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center">
            <button
                    type="submit"
                    class="w-full px-5 py-3 font-semibold bg-purple-950 text-white rounded-lg transition-all duration-300 hover:bg-white hover:text-purple-950 hover:border-2 hover:border-purple-950 shadow-md"
            >
              Send Message
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>
  <div id="footer-container"></div>
      <script type="module" src="/scripts/loadFooter.js"></script>
  </div>
</section>
</section>

<!-- Contact Modal (hidden by default) -->
<div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
    <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
    <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
    <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
      <div>
        <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
      </div>
      <div>
        <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
        <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
      </div>
      <div class="flex justify-center">
        <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>
<!-- Footer -->

</body>
</html>

<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Projects - RISE STEM</title>
    <link href="/src/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/src/animations.css">
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- Header -->
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>

    <section class="min-h-screen bg-gray-50 w-full relative overflow-hidden pt-32">
      <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 z-30 relative pb-8">
        <div class="bg-white p-4 rounded-2xl border border-gray-300 shadow-md justify-center">
          <div class="flex justify-center text-center">
            <h2 class=" text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
              Explore Our <span class="font-extrabold">Projects</span>
            </h2>
          </div>
          <div class="flex justify-center items-center pt-8 sm:px-16 lg:px-32 flex-col">
            <p class=" text-md text-gray-500 leading-relaxed text-center sm:px-4 md:px-8 lg:px-16 xl:px-32">
              Discover the hands-on projects and programs that RISE offers to spark curiosity, creativity, and innovation in STEM!
            </p>
          </div>
        </div>

        <div class="pt-12 w-full flex justify-center z-30">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-full">
            <!-- Curriculum Card -->
            <div class=" overflow-hidden rounded-lg border border-gray-300 bg-white z-30 shadow-lg">
              <div class=" w-full bg-cover bg-center" style="background-image: url('/public/enpeng_leading.jpeg'); height: 200px;"></div>
              <div class=" flex flex-col justify-center items-center p-4 space-y-4">
                <span class="text-xl font-bold text-purple-950 sm:text-2xl">Curriculum</span>
                <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                  Structured, hands-on lessons designed to guide students from basic concepts to advanced STEM skills while keeping learning fun and interactive.
                </div>
                <a href="curriculum" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                  View Curriculum
                </a>
              </div>
            </div>
            <!-- Kits Card -->
            <div class=" overflow-hidden rounded-lg border border-gray-300 bg-white z-30 shadow-lg">
              <div class=" w-full bg-cover bg-center" style="background-image: url('/public/rise_kit.png'); height: 200px;"></div>
              <div class=" flex flex-col justify-center items-center p-4 space-y-4">
                <span class="text-xl font-bold text-purple-950 sm:text-2xl">Kits</span>
                <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                  Custom-designed STEM kits for hands-on building and experimentation, making science and technology accessible for all students.
                </div>
                <a href="kits" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                  Explore Kits
                </a>
              </div>
            </div>
            <!-- Workshops Card -->
            <div class=" overflow-hidden rounded-lg border border-gray-300 bg-white z-30 shadow-lg">
              <div class=" w-full bg-cover bg-center" style="background-image: url('/public/IMG_0185.JPG'); height: 200px;"></div>
              <div class=" flex flex-col justify-center items-center p-4 space-y-4">
                <span class="text-xl font-bold text-purple-950 sm:text-2xl">Workshops</span>
                <div class="text-center text-sm px-4 text-gray-500 sm:text-md">
                  Interactive workshops where students collaborate, compete, and problem-solve together in a dynamic learning environment.
                </div>
                <a href="workshops" class="px-6 py-2 text-white font-bold bg-purple-950 rounded-full transform transition-all duration-300 hover:scale-110 hover:text-white">
                  Learn More
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
      <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
      <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
      <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
        <div>
          <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
        </div>
        <div>
          <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
        </div>
      </form>
    </div>
  </div>
      <!-- Footer -->
      <div id="footer-container"></div>
      <script type="module" src="/scripts/loadFooter.js"></script>
    </section>
  </body>
  <script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>
</html>

<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>About - RISE STEM</title>
  <link href="/src/style.css" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .parallax-container {
      transition:
              transform 0.2s cubic-bezier(.03,.98,.52,.99),
              box-shadow 0.2s cubic-bezier(.03,.98,.52,.99);
      transform:
              perspective(600px)
              rotateX(var(--rX, 0deg))
              rotateY(var(--rY, 0deg));
      position: relative; /* Ensure relative for the glow absolutely inside */
      overflow: hidden; /* So the glow can overflow */
    }
    .parallax-active {
      box-shadow: 0 8px 32px 0 rgba(76,29,149,0.18);
      transition: box-shadow 0.2s;
    }
    .glow-effect {
      pointer-events: none;
      mix-blend-mode: lighten;
      filter: blur(32px); /* Subtle blur for soft glow */
    }
  </style>
</head>
<body>

<!-- Header -->
<div id="header-container"></div>
<script type="module" src="scripts/loadHeader.js"></script>
<main class="min-h-screen bg-gray-50 w-full">

  <!-- Hero Section -->
  <section class="bg-gray-50 w-full relative">
    <div class="w-full h-[60vh] bg-[url('/public/excitement.jpeg')] bg-cover bg-[center_30%] bg-no-repeat flex items-center justify-center relative z-0">
      <!-- Gradient Overlay -->
      <div class="h-[60vh] absolute inset-0 bg-black opacity-50 z-0"></div>
      <div class="flex flex-col text-center items-center justify-center px-6 sm:px-16 md:px-32 relative z-10">
        <h1 class="mt-16 text-3xl font-extrabold text-white md:text-4xl xl:text-5xl justify-center">
          We're rising together to unlock opportunity through STEM.
        </h1>
      </div>
    </div>

    <!-- Mission & Vision Section -->
    <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 mb-8 -mt-8 relative z-30">
      <div class="bg-white p-6 rounded-2xl border border-gray-300 shadow-md">
        <div class="flex justify-center text-center">
          <h2 class="text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
            Our <span class="font-extrabold">Mission & Vision</span>
          </h2>
        </div>
        <div class="flex flex-col gap-6 justify-center items-center pt-8 sm:px-16 lg:px-32">
          <p class="text-lg md:text-xl text-purple-900 font-semibold text-center px-2">Mission</p>
          <p class="text-md text-gray-500 leading-relaxed text-center sm:px-4 md:px-8 lg:px-16 xl:px-32">
            To make STEM education more inclusive, accessible, and empowering for every student.
          </p>
          <p class="text-lg md:text-xl text-purple-900 font-semibold text-center px-2 mt-6">Vision</p>
          <p class="text-md text-gray-500 leading-relaxed text-center sm:px-4 md:px-8 lg:px-16 xl:px-32">
            We envision a world where all young people, regardless of background, can rise to their full potential through equitable access to science, technology, engineering, and math education.
          </p>
        </div>
      </div>
    </div>

    <!-- Values Section: Parallax Cards -->
    <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 my-8 z-30">
      <div class="bg-white p-6 rounded-2xl border border-gray-300 shadow-md">
        <div class="flex justify-center text-center">
          <h2 class="text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
            Our <span class="font-extrabold">Values</span>
          </h2>
        </div>
        <div class="flex flex-wrap justify-center gap-6 pt-8">
          <!-- Card 1 -->
          <div class="parallax-wrap w-64">
            <div class="parallax-container bg-transparent rounded-xl shadow-sm border border-gray-200 w-64 p-6 flex flex-col items-center text-center">
              <span class="material-symbols-outlined text-4xl text-purple-700 mb-2">balance</span>
              <p class="text-lg font-bold text-purple-900">Equity</p>
              <p class="text-gray-600 text-sm mt-2">We champion equal access and representation in STEM.</p>
            </div>
          </div>
          <!-- Card 2 -->
          <div class="parallax-wrap w-64">
            <div class="parallax-container bg-transparent rounded-xl shadow-sm border border-gray-200 w-64 p-6 flex flex-col items-center text-center">
              <span class="material-symbols-outlined text-4xl text-purple-700 mb-2">bolt</span>
              <p class="text-lg font-bold text-purple-900">Empowerment</p>
              <p class="text-gray-600 text-sm mt-2">We uplift youth to become future innovators.</p>
            </div>
          </div>
          <!-- Card 3 -->
          <div class="parallax-wrap w-64">
            <div class="parallax-container bg-transparent rounded-xl shadow-sm border border-gray-200 w-64 p-6 flex flex-col items-center text-center">
              <span class="material-symbols-outlined text-4xl text-purple-700 mb-2">lightbulb</span>
              <p class="text-lg font-bold text-purple-900">Curiosity</p>
              <p class="text-gray-600 text-sm mt-2">We celebrate questioning, learning, and creativity.</p>
            </div>
          </div>
          <!-- Card 4 -->
          <div class="parallax-wrap w-64">
            <div class="parallax-container bg-transparent  rounded-xl shadow-sm border border-gray-200 w-64 p-6 flex flex-col items-center text-center">
              <span class="material-symbols-outlined text-4xl text-purple-700 mb-2">diversity_3</span>
              <p class="text-lg font-bold text-purple-900">Collaboration</p>
              <p class="text-gray-600 text-sm mt-2">We grow stronger together through teamwork.</p>
            </div>
          </div>
        </div>
        <!-- Parallax JS -->
        <script>
          class parallaxTiltEffect {
            constructor({element, tiltEffect}) {
              this.element = element;
              this.container = this.element.querySelector(".parallax-container");
              this.size = [this.container.offsetWidth, this.container.offsetHeight];
              [this.w, this.h] = this.size;
              this.tiltEffect = tiltEffect;
              this.mouseOnComponent = false;

              this.handleMouseMove = this.handleMouseMove.bind(this);
              this.handleMouseEnter = this.handleMouseEnter.bind(this);
              this.handleMouseLeave = this.handleMouseLeave.bind(this);
              this.defaultStates = this.defaultStates.bind(this);
              this.setProperty = this.setProperty.bind(this);
              this.init = this.init.bind(this);

              this.init();
            }

            handleMouseMove(event) {
              const bounds = this.container.getBoundingClientRect();
              const offsetX = event.clientX - bounds.left;
              const offsetY = event.clientY - bounds.top;

              let X;
              let Y;

              if (this.tiltEffect === "reverse") {
                X = ((offsetX - (this.w/2)) / 3) / 3;
                Y = (-(offsetY - (this.h/2)) / 3) / 3;
              } else {
                X = (-(offsetX - (this.w/2)) / 3) / 3;
                Y = ((offsetY - (this.h/2)) / 3) / 3;
              }

              this.setProperty('--rY', X.toFixed(2) + 'deg');
              this.setProperty('--rX', Y.toFixed(2) + 'deg');
              this.setProperty('--bY', (80 - (X/4).toFixed(2)) + '%');
              this.setProperty('--bX', (50 - (Y/4).toFixed(2)) + '%');
            }

            handleMouseEnter() {
              this.mouseOnComponent = true;
              this.container.classList.add("parallax-active");
            }

            handleMouseLeave() {
              this.mouseOnComponent = false;
              this.defaultStates();
            }

            defaultStates() {
              this.container.classList.remove("parallax-active");
              this.setProperty('--rY', '0deg');
              this.setProperty('--rX', '0deg');
              this.setProperty('--bY', '80%');
              this.setProperty('--bX', '50%');
            }

            setProperty(p, v) {
              this.container.style.setProperty(p, v);
            }

            init() {
              this.element.addEventListener('mousemove', this.handleMouseMove);
              this.element.addEventListener('mouseenter', this.handleMouseEnter);
              this.element.addEventListener('mouseleave', this.handleMouseLeave);
            }
          }

          document.querySelectorAll('.parallax-wrap').forEach((wrap, idx) => {
            new parallaxTiltEffect({
              element: wrap,
              tiltEffect: idx % 2 === 0 ? 'normal' : 'reverse'
            });
          });
        </script>
        <!-- Card Glow Effect JS -->
        <script type="module">
          document.querySelectorAll('.parallax-wrap').forEach((wrap) => {
            const container = wrap.querySelector('.parallax-container');

            // Create the glow element
            const glow = document.createElement('div');
            glow.style.position = 'absolute';
            glow.style.pointerEvents = 'none';
            glow.style.zIndex = '1';
            glow.style.width = '80px';
            glow.style.height = '80px';
            glow.style.borderRadius = '50%';
            glow.style.background = 'radial-gradient(circle, rgba(168,85,247,0.26) 0%, rgba(139,92,246,0.13) 60%, rgba(139,92,246,0.06) 100%)';
            glow.style.transition = 'opacity 0.18s, transform 0.09s';
            glow.style.opacity = '0';
            glow.style.transform = 'translate(-50%, -50%) scale(0.95)';
            glow.classList.add('glow-effect');

            // Make sure the card container is position:relative
            container.style.position = 'relative';
            container.appendChild(glow);

            function showGlow() {
              glow.style.opacity = '1';
            }

            function hideGlow() {
              glow.style.opacity = '0';
            }

            function moveGlow(e) {
              const bounds = container.getBoundingClientRect();
              const x = e.clientX - bounds.left;
              const y = e.clientY - bounds.top;
              // Subtle scale based on distance to center (parallax "tilt" illusion)
              const centerX = bounds.width / 2;
              const centerY = bounds.height / 2;
              const dx = Math.abs(x - centerX) / centerX;
              const dy = Math.abs(y - centerY) / centerY;
              const scale = 1.05 - (dx + dy) * 0.17;
              glow.style.left = `${x}px`;
              glow.style.top = `${y}px`;
              glow.style.transform = `translate(-50%, -50%) scale(${scale})`;
              // Optionally, you can also add a blur or more intense glow based on scale.
            }

            // Attach listeners to the parallax-wrap, so the glow is only active on hover
            wrap.addEventListener('mouseenter', showGlow);
            wrap.addEventListener('mousemove', moveGlow);
            wrap.addEventListener('mouseleave', hideGlow);
          });
        </script>
      </div>
    </div>
  </section>

  <!-- Partners Card -->
  <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 my-8 z-30">
    <div class="bg-white p-6 rounded-2xl border border-gray-300 shadow-md">
      <div class="flex justify-center text-center">
        <h2 class="text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
          Our <span class="font-extrabold">Partners</span>
        </h2>
      </div>
      <div class="flex justify-center items-center pt-8 sm:px-16 lg:px-32 flex-col">
        <p class="text-md text-gray-500 leading-relaxed text-center sm:px-4 md:px-8 lg:px-16 xl:px-32">
          RISE's work is made possible through the support of our partners, who share our vision of empowering students through STEM education.
          Together, we are creating a brighter future for the next generation of innovators and leaders.
        </p>
      </div>
      <div class="px-6 sm:px-12 md:px-24 lg:px-32 pb-12 flex justify-center">
        <div class="w-full max-w-screen-lg bg-white rounded-lg p-6 sm:p-8 flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
          <img src="/public/agape.png" alt="Agape Youth & Family Center Logo" class="object-contain w-32 sm:w-40 h-auto md:w-48">
          <div class="flex flex-col text-center md:text-left">
            <h2 class="font-bold text-purple-950 text-xl sm:text-2xl">
              Agape Youth & Family Center
            </h2>
            <p class="text-gray-500 text-sm sm:text-md leading-relaxed mt-3">
              Agape Youth & Family Center is dedicated to empowering underserved students with educational programs, mentorship, and leadership opportunities. By providing academic support and community resources, Agape helps students achieve success in school and beyond.
            </p>
            <a href="https://agapeatlanta.org" target="_blank" class="text-white hover:text-white bg-purple-950 border border-transparent font-semibold rounded-md text-md px-6 py-2.5 sm:mr-2 lg:mr-0 transition duration-300 mt-4 self-center md:self-start transform transition-all hover:scale-110">
              Learn More
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
</main>

<!-- Contact Modal (hidden by default) -->
<div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
    <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
    <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
    <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
      <div>
        <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
      </div>
      <div>
        <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
        <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
      </div>
      <div class="flex justify-center">
        <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>

</body>
</html>

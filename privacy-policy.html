<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Privacy Policy - RISE STEM</title>
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="/src/style.css" rel="stylesheet" />
    <link rel="stylesheet" href="/src/animations.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;700&display=swap" rel="stylesheet" />
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
  <div id="header-container"></div>
  <script type="module" src="/scripts/loadHeader.js"></script>
  <section class="bg-gray-50 w-full text-gray-800 relative overflow-hidden font-poppins">
    <!-- Main Content -->
    <section class="max-w-4xl mx-auto px-6 py-16 pt-32">
      <!-- Header -->
      <h1 class="text-4xl font-extrabold text-purple-950 mb-6 text-center">Privacy Policy</h1>
      <p class="text-center text-gray-500 text-sm mb-8">Last updated: July 6, 2025</p>

      <p class="mb-4">
        At RISE STEM, your privacy is important to us. This Privacy Policy explains how we collect, use, and protect any personal information you provide to us through our website.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">1. Information We Collect</h2>
      <p class="mb-4">
        When you submit a message through our contact form, we collect:
      </p>
      <ul class="list-disc list-inside mb-4">
        <li>Your email address</li>
        <li>Any message or information you include in the form</li>
      </ul>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">2. How We Use Your Information</h2>
      <p class="mb-4">
        We only use the information you provide to:
      </p>
      <ul class="list-disc list-inside mb-4">
        <li>Respond to your inquiries</li>
        <li>Provide support related to your message</li>
      </ul>
      <p class="mb-4">
        We do not use your data for marketing, advertising, or analytics purposes.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">3. How Your Information Is Handled</h2>
      <p class="mb-4">
        Our contact form is powered by <strong>Formspree</strong>, a third-party service that securely processes and forwards your message to our email inbox.
        You can review their privacy policy here:
        <a href="https://formspree.io/legal/privacy-policy" class="text-purple-700 underline" target="_blank">https://formspree.io/legal/privacy-policy</a>.
      </p>
      <p class="mb-4">
        We do not sell or share your personal information with any other third parties.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">4. Data Security</h2>
      <p class="mb-4">
        We take reasonable precautions to protect the information you provide from unauthorized access or disclosure. However, no method of transmission over the internet is 100% secure.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">5. Your Rights</h2>
      <p class="mb-4">
        If you would like us to delete your submitted message or personal information, please contact us at <a href="mailto:<EMAIL>" class="text-purple-700 underline"><EMAIL></a>.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">6. Changes to This Policy</h2>
      <p class="mb-4">
        We may update this Privacy Policy from time to time. Any changes will be posted on this page with an updated revision date.
      </p>

      <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">7. Contact Us</h2>
      <p>
        If you have any questions about this Privacy Policy, please email us at <a href="mailto:<EMAIL>" class="text-purple-700 underline"><EMAIL></a>.
      </p>

        <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
      <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
      <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
      <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
        <div>
          <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
        </div>
        <div>
          <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
        </div>
      </form>
    </div>
  </div>
    </section>
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>
    <script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>
</html>

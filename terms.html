<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Terms of Use - RISE STEM</title>
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="/src/style.css" rel="stylesheet" />
    <link rel="stylesheet" href="/src/animations.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;700&display=swap" rel="stylesheet" />
    <style>
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
  <body>
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>
    <section class="bg-gray-50 w-full text-gray-800 relative overflow-hidden font-poppins">
      <!-- Main Content -->
      <section class="max-w-4xl mx-auto px-6 py-16 pt-32">
        <!-- Header -->
        <h1 class="text-4xl font-extrabold text-purple-950 mb-6 text-center">Terms of Use</h1>
        <p class="text-center text-gray-500 text-sm mb-8">Last updated: July 6, 2025</p>

        <p class="mb-4">
          These Terms of Use ("Terms") govern your use of the RISE STEM website (the "Site"). By accessing or using this Site, you agree to these Terms. If you do not agree, please do not use the Site.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">1. Use of Site</h2>
        <p class="mb-4">
          RISE STEM provides information for educational and nonprofit purposes only. You may use this Site for personal, non-commercial use. You may not copy, reproduce, or distribute content without permission.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">2. User Submissions</h2>
        <p class="mb-4">
          If you submit a message through our contact form, you agree not to submit any harmful, abusive, or illegal content. We reserve the right to ignore or delete inappropriate submissions.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">3. Intellectual Property</h2>
        <p class="mb-4">
          All content on this Site, including text, images, logos, and designs, is the property of RISE STEM unless otherwise credited. Unauthorized use of our materials is prohibited.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">4. Third-Party Services</h2>
        <p class="mb-4">
          Our contact form is powered by Formspree, a third-party service. Your use of the form is also subject to Formspree’s terms and privacy policy.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">5. Disclaimer</h2>
        <p class="mb-4">
          The information on this Site is provided "as is" without warranties of any kind. We do our best to keep information accurate and up to date, but we make no guarantees.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">6. Limitation of Liability</h2>
        <p class="mb-4">
          RISE STEM will not be liable for any damages arising from your use of this Site or reliance on its content.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">7. Changes to Terms</h2>
        <p class="mb-4">
          We may update these Terms from time to time. Changes will be posted on this page with an updated revision date.
        </p>

        <h2 class="text-2xl font-semibold text-purple-800 mt-8 mb-2">8. Contact</h2>
        <p>
          If you have questions about these Terms, please contact us at
          <a href="mailto:<EMAIL>" class="text-purple-700 underline"><EMAIL></a>.
        </p>
        <div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
      <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
      <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
      <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
        <div>
          <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
        </div>
        <div>
          <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
          <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
        </div>
      </form>
    </div>
  </div>
      </section>
      <div id="footer-container"></div>
      <script type="module" src="/scripts/loadFooter.js"></script>
    </section>
  </body>
  <script>
  document.addEventListener('header:loaded', function() {
    setupContactModal();
  });

  document.addEventListener('DOMContentLoaded', function() {
    setupContactModal();
  });

  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>
</html>

<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>What We Do - Rise Engineering & Coding</title>
  <link href="src/style.css" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>

<!-- Header -->
<div id="header-container"></div>
<script type="module" src="scripts/loadHeader.js"></script>
<!-- Main Content -->
<main class="min-h-screen bg-gray-50 w-full">

  <!-- Hero Section -->
  <section class="w-full">
    <div class="w-full h-[60vh] bg-[url('/public/mousetrap_car.jpeg')] bg-cover bg-[center_40%] bg-no-repeat flex items-center justify-center relative">
      <!-- Gradient Overlay -->
      <div class="h-[60vh] absolute inset-0 bg-black opacity-50"></div>
      <div class="flex flex-col text-center items-center justify-center px-6 sm:px-16 md:px-32 z-10">
        <h1 class="mt-16 text-3xl font-extrabold text-white md:text-4xl xl:text-5xl justify-center">
          What We Do
        </h1>
      </div>
    </div>
  </section>

  <!-- What We Do Section -->
  <section id="What-We-Do" class="w-full px-4 sm:px-6 md:px-16 lg:px-24 -mt-8 z-20 relative pb-12 scroll-mt-24">
    <div class="bg-white p-4 sm:p-8 rounded-2xl border border-gray-300 z-30 shadow-md justify-center">
      <div class="flex justify-center text-center">
        <h2 class="text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
          Our <span class="font-extrabold">Approach</span>
        </h2>
      </div>

      <div class="flex justify-center items-center pt-8 sm:px-4 md:px-8 lg:px-16 xl:px-32 flex-col">
        <p class="text-md text-gray-500 leading-relaxed text-center">
          RISE's mission has always been clear: to inspire the next generation of engineers and scientists by sparking curiosity and providing hands-on learning opportunities. Through engaging projects and real-world applications, we're redefining STEM education to make it accessible and exciting for every student.
        </p>
      </div>
      <!-- Section with rotated images and bullet points -->
      <div class="flex flex-col md:flex-row justify-center items-start  md:gap-16 px-0 sm:px-6 py-8 sm:py-12">
        <!-- Left Side: Images with slight rotation -->
        <div class="relative w-full md:w-1/2 flex justify-center items-center min-h-[400px] mb-12 md:mb-0" id="image-container">
          <!-- Container for each image and caption -->
          <div class="absolute w-64 sm:w-80 md:w-[28rem] rounded-xl shadow-lg transform rotate-3 z-10 overflow-hidden bg-[#fcfbfd]">
            <img src="/public/kid_holding_car.jpeg" alt="Placeholder" class="w-full block">
            <div class="p-3 text-center text-gray-700 font-semibold">Completed mechanical engineering project</div>
          </div>

          <!-- Second container -->
          <div class="absolute w-64 sm:w-80 md:w-[28rem] rounded-xl shadow-lg transform -rotate-3 ml-4 mt-4 z-20 overflow-hidden bg-[#fcfbfd]">
            <img src="/public/enpeng_testing.jpeg" alt="Placeholder" class="w-full block">
            <div class="p-3 text-center text-gray-700 font-semibold">Bridge building competition</div>
          </div>

          <!-- Third container -->
          <div class="absolute w-64 sm:w-80 md:w-[28rem] rounded-xl shadow-lg transform rotate-6 ml-8 mt-8 z-30 overflow-hidden bg-[#fcfbfd]">
            <img src="/public/IMG_0154 2.JPG" alt="RISE Mentor Teaching a Class" class="w-full block">
            <div class="p-3 text-center text-gray-700 font-semibold">Mentor hosting a RISE class</div>
          </div>
        </div>

        <!-- Right Side: Bullet Points -->
        <div class="w-full md:w-1/2 text-left space-y-8 flex flex-col justify-center min-h-[400px]">
          <div class="flex items-start gap-4">
            <!-- Big Circle with Number -->
            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 border-purple-700 text-purple-700 font-bold flex-shrink-0">
              1
            </div>
            <!-- Bullet Point Text -->
            <div>
              <h3 class="text-2xl font-bold text-purple-900 mb-2">Create Engaging Curriculum</h3>
              <p class="text-gray-600"><a href="/rise/curriculum" class="text-purple-700 underline">RISE's curriculum</a> uses hands-on projects <br> and real-world applications to spark <br> engagement and inspire kids to see <br> STEM as an exciting path forward.</p>
            </div>
          </div>
          <div class="flex items-start gap-4">
            <!-- Big Circle with Number -->
            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 border-purple-700 text-purple-700 font-bold flex-shrink-0">
              2
            </div>
            <!-- Bullet Point Text -->
            <div>
              <h3 class="text-2xl font-bold text-purple-900 mb-2">Host RISE Sessions</h3>
              <p class="text-gray-600">RISE sessions aren't your typical classroom experience. <br> They're designed around collaborative, hands-on <br> projects that bring STEM to life and <br> turn learning into a real-world adventure.</p>
            </div>
          </div>
          <div class="flex items-start gap-4">
            <!-- Big Circle with Number -->
            <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 border-purple-700 text-purple-700 font-bold flex-shrink-0">
              3
            </div>
            <!-- Bullet Point Text -->
            <div>
              <h3 class="text-2xl font-bold text-purple-900 mb-2">Innovate Creative Solutions</h3>
              <p class="text-gray-600">At RISE, we are constantly innovating and creating <br> new solutions to make STEM education to advance our mission. <br> From designing new ways to engage with students, <br> to creating new tools and resources, we believe <br> that innovation on our part is what will inspire <br>the next generation of engineers and scientists.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Our Impact Section-->
  <section id="Impact" class="w-full px-4 sm:px-6 md:px-16 lg:px-24 mt-4 z-20 relative pb-12 scroll-mt-24">
    <div class="bg-white p-4 sm:p-8 rounded-2xl border border-gray-300 z-30 shadow-md justify-center">
      <div class="flex justify-center text-center">
        <h2 class="text-3xl font-poppins font-semibold text-purple-950 md:text-4xl">
          Our <span class="font-extrabold">Impact</span>
        </h2>
      </div>

      <div class="flex justify-center items-center pt-8 sm:px-4 md:px-8 lg:px-16 xl:px-32 flex-col">
        <p class="text-md text-gray-500 leading-relaxed text-center">
          So far on our journey, RISE and its members have made a significant impact in our community. We are proud of our impact so far but we know that there is so much more to do. We are committed to continuing our work and expanding our reach to empower even more students through STEM education.
        </p>
      </div>

      <!-- Impact Statistics Infographic -->
      <div class="flex flex-col sm:flex-row justify-center items-center gap-8 sm:gap-16 pt-8 sm:pt-16 pb-8">

        <!-- Service Hours Stat -->
        <div class="flex flex-col items-center text-center px-4 sm:px-8 py-4 sm:py-6">
          <!-- Clock Icon -->
          <div class="w-20 h-20 sm:w-24 sm:h-24 mb-4 sm:mb-6 flex items-center justify-center bg-purple-100 rounded-full">
            <svg class="w-10 h-10 sm:w-12 sm:h-12 text-purple-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-4xl sm:text-5xl font-extrabold text-purple-950 mb-2 sm:mb-3">150+</div>
          <div class="text-base sm:text-lg font-semibold text-gray-600 uppercase tracking-wide">Service Hours</div>
        </div>

        <!-- RISE Camps Stat -->
        <div class="flex flex-col items-center text-center px-4 sm:px-8 py-4 sm:py-6">
          <!-- Tent/Camp Icon -->
          <div class="w-20 h-20 sm:w-24 sm:h-24 mb-4 sm:mb-6 flex items-center justify-center bg-purple-100 rounded-full">
            <svg class="w-10 h-10 sm:w-12 sm:h-12 text-purple-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
            </svg>
          </div>
          <div class="text-4xl sm:text-5xl font-extrabold text-purple-950 mb-2 sm:mb-3">3</div>
          <div class="text-base sm:text-lg font-semibold text-gray-600 uppercase tracking-wide">RISE Camps</div>
        </div>

        <!-- Students Reached Stat -->
        <div class="flex flex-col items-center text-center px-4 sm:px-8 py-4 sm:py-6">
          <!-- Students/People Icon -->
          <div class="w-20 h-20 sm:w-24 sm:h-24 mb-4 sm:mb-6 flex items-center justify-center bg-purple-100 rounded-full">
            <svg class="w-10 h-10 sm:w-12 sm:h-12 text-purple-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
          </div>
          <div class="text-4xl sm:text-5xl font-extrabold text-purple-950 mb-2 sm:mb-3">50+</div>
          <div class="text-base sm:text-lg font-semibold text-gray-600 uppercase tracking-wide">Students Reached</div>
        </div>

      </div>
    </div>
  </section>

  <!-- Footer -->
  <section class="bg-white w-full">
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>
  </section>
</main>



<!-- Contact Modal (hidden by default) -->
<div id="contact-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
    <button id="close-contact-modal" class="absolute top-2 right-2 text-gray-400 hover:text-purple-700 text-2xl font-bold">&times;</button>
    <h3 class="text-3xl font-extrabold text-purple-950 mb-4 text-center">Contact Us</h3>
    <form action="https://formspree.io/f/xwpvqjel" method="POST" class="space-y-4">
      <div>
        <label for="modal-email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" name="email" id="modal-email" required class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your email" />
      </div>
      <div>
        <label for="modal-message" class="block text-sm font-medium text-gray-700">Message</label>
        <textarea name="message" id="modal-message" required rows="4" class="w-full px-4 py-2 border bg-white border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black" placeholder="Enter your message"></textarea>
      </div>
      <div class="flex justify-center">
        <button type="submit" class="w-full px-4 py-2 font-bold bg-purple-950 text-white rounded-md transition-all duration-200">Send</button>
      </div>
    </form>
  </div>
</div>

<!-- Scripts -->
<script>
  // Image container click functionality
  const container = document.getElementById('image-container');
  const cards = container.querySelectorAll('div.absolute');
  let currentTopIndex = cards.length - 1;

  container.addEventListener('click', () => {
    const topCard = cards[currentTopIndex];
    topCard.style.zIndex = 10;

    cards.forEach((card, i) => {
      if (i !== currentTopIndex) {
        const z = parseInt(card.style.zIndex || 10, 10);
        card.style.zIndex = z + 10;
      }
    });

    currentTopIndex = (currentTopIndex - 1 + cards.length) % cards.length;
  });
</script>

<script>
  function setupContactModal() {
    const contactModal = document.getElementById('contact-modal');
    const closeContactBtn = document.getElementById('close-contact-modal');

    // Find all "Contact Us" buttons/links in header
    const openContactBtns = Array.from(document.querySelectorAll('button, a')).filter(
            el => el.textContent && el.textContent.trim().toLowerCase() === "contact us"
    );

    openContactBtns.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (contactModal) contactModal.classList.remove('hidden');
      });
    });

    if (closeContactBtn) {
      closeContactBtn.addEventListener('click', function() {
        contactModal.classList.add('hidden');
      });
    }

    if (contactModal) {
      contactModal.addEventListener('click', function(e) {
        if (e.target === contactModal) {
          contactModal.classList.add('hidden');
        }
      });
    }
  }
</script>

</body>
</html>

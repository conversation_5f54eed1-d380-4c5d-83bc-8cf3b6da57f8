<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Scroll Video - RISE STEM</title>
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <link rel="icon" type="image/png" href="/public/favicon-32x32.png" />
    <link rel="shortcut icon" href="/public/favicon.ico" />
    <link rel="apple-touch-icon" href="/public/apple-touch-icon.png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <style>
      html { scroll-behavior: smooth; }
      body { margin: 0; padding: 0; font-family: 'Poppins', sans-serif; background-color: #f9fafb; }

      /* Container & Layout */
      .animation-container { height: 80vh; overflow: hidden; position: relative; display: flex; align-items: center; justify-content: center; margin-bottom: 2rem; }
      .scroll-video-container { position: absolute; right: 5%; top: 50%; transform: translateY(-50%); width: 60%; height: 80%; max-width: none; z-index: 10; display: flex; align-items: center; justify-content: center; }
      .scroll-video-frame { max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; }
      .scroll-text-container { position: absolute; left: 5%; top: 50%; transform: translateY(-50%); width: 30%; max-width: 400px; z-index: 20; height: 200px; }

      /* Text sections */
      .scroll-text-section { position: absolute; width: 100%; top: 0; left: 0; opacity: 0; transform: translateX(-100px); transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); pointer-events: none; }
      .scroll-text-section.active { opacity: 1; transform: translateX(0); pointer-events: auto; }
      .scroll-text-section.exit-right { opacity: 0; transform: translateX(100px); }
      .scroll-text-section.exit-left  { opacity: 0; transform: translateX(-100px); }

      .scroll-text-section h2 { font-size: 2rem; font-weight: bold; color: #581c87; margin-bottom: 1.5rem; line-height: 1.2; }
      .scroll-text-section p  { font-size: 1.1rem; color: #374151; line-height: 1.6; margin-bottom: 1rem; }

      /* Progress Bar */
      .progress-bar { position: fixed; top: 0; left: 0; height: 4px; background: linear-gradient(90deg, #3b82f6, #8b5cf6); z-index: 100; transition: width 0.2s ease; }

      /* Post-animation */
      .post-animation-content {
        min-height: 100vh;
        padding: 4rem 2rem;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease, visibility 0.5s ease;
        scroll-margin-top: 80px; /* ensure header offset */
      }
      .post-animation-content.visible {
        opacity: 1;
        visibility: visible;
      }
      .post-animation-content h1 { font-size: 3rem; font-weight: bold; color: #581c87; margin-bottom: 2rem; text-align: center; }
      .post-animation-content h2 { font-size: 2rem; font-weight: bold; color: #581c87; margin: 3rem 0 1.5rem; }
      .post-animation-content p  { font-size: 1.1rem; color: #374151; line-height: 1.7; margin-bottom: 1.5rem; }

      /* Responsive */
      @media (max-width: 768px) {
        .scroll-video-container { width: 90%; height: 70vh; right: 5%; top: 15%; }
        .scroll-text-container  { width: 90%; left: 5%; top: 85%; }
        .scroll-text-section h2  { font-size: 1.5rem; }
        .scroll-text-section p   { font-size: 1rem; }
      }
      @media (max-width: 640px) {
        .animation-container  { flex-direction: column; padding: 2rem 1rem; }
        .scroll-video-container { position: relative; width: 100%; height: 60vh; right: auto; top: auto; transform: none; margin-bottom: 2rem; }
        .scroll-text-container   { position: relative; width: 100%; left: auto; top: auto; transform: none; }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>
    
    <div class="progress-bar" id="progressBar"></div>

    <!-- Single White Content Box containing everything -->
    <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 py-8 bg-gray-50">
      <div class="bg-white rounded-2xl border border-gray-300 shadow-md p-8">

        <!-- Animation Container (top section of the white box) -->
        <div class="animation-container" id="animationContainer">
          <div class="scroll-video-container">
            <img
              id="videoFrame"
              class="scroll-video-frame"
              src="/public/v5_webp/turtleExplodev500001.webp"
              alt="Video Frame"
            />
          </div>
          <div class="scroll-text-container">
            <div class="scroll-text-section" id="textSection1">
              <h2>Innovation in Learning</h2>
              <p>The Turtle Kit offers an innovative approach to STEM education, encouraging students to explore new ideas through hands-on, interactive experiences. It’s a playful introduction to complex concepts, fostering curiosity and confidence in young learners.</p>
            </div>

            <div class="scroll-text-section" id="textSection2">
              <h2>Fun Meets Function</h2>
              <p>Learning feels less like work and more like discovery. The Turtle Kit blends creativity and critical thinking, making STEM concepts approachable and enjoyable while still building real-world understanding.</p>
            </div>

            <div class="scroll-text-section" id="textSection3">
              <h2>Scientific Discovery</h2>
              <p>With every experiment and exploration, students uncover the “why” behind how things work. The Turtle Kit sparks a deeper interest in the natural world, inspiring the next generation of thinkers, builders, and problem solvers.</p>
            </div>
          </div>
        </div>

        <!-- Post-Animation Content (within same white box) -->
        <div class="post-animation-content">
          <!-- New header -->
          <h1 class="text-4xl font-bold text-purple-950 mb-6">Code Comes to Life</h1>

          <!-- Mock IDE window: bigger text, tighter lines, no scroll -->
          <div class="bg-gray-900 rounded-lg overflow-hidden font-mono text-xl leading-snug shadow-lg w-full md:w-[48%] h-[480px]">
            <!-- Window header bar -->
            <div class="bg-gray-800 flex items-center space-x-2 px-3 py-2">
              <span class="w-3 h-3 bg-red-500 rounded-full"></span>
              <span class="w-3 h-3 bg-yellow-500 rounded-full"></span>
              <span class="w-3 h-3 bg-green-500 rounded-full"></span>
              <span class="ml-2 text-gray-300">main.cpp</span>
            </div>
            <!-- Code area -->
            <div class="flex h-full">
              <!-- Line numbers -->
              <div class="text-gray-600 select-none pr-6 bg-gray-900">
                <div class="px-2 py-1">1</div>
                <div class="px-2 py-1">2</div>
                <div class="px-2 py-1">3</div>
                <div class="px-2 py-1">4</div>
                <div class="px-2 py-1">5</div>
                <div class="px-2 py-1">6</div>
                <div class="px-2 py-1">7</div>
                <div class="px-2 py-1">8</div>
                <div class="px-2 py-1">9</div>
                <div class="px-2 py-1">10</div>
                <div class="px-2 py-1">11</div>
              </div>
              <!-- Code lines -->
              <div class="text-gray-100 flex-1">
                <div class="px-2 py-1"><span class="text-blue-400">#include</span> <span class="text-green-400">&lt;Stepper.h&gt;</span></div>
                <div class="px-2 py-1"><span class="text-blue-400">#include</span> <span class="text-green-400">&lt;Servo.h&gt;</span></div>
                <div class="px-2 py-1"><span class="text-blue-400">#include</span> <span class="text-green-400">&lt;math.h&gt;</span></div>
                <div class="px-2 py-1"><span class="text-blue-400">#include</span> <span class="text-green-400">&lt;kit.h&gt;</span></div>
                <!-- blank line -->
                <div class="px-2 py-1">&nbsp;</div>
                <!-- highlighted main signature -->
                <div class="px-2 py-1 bg-gray-800"><span class="text-blue-300">int</span> <span class="text-white">main</span>() {</div>
                <div class="px-2 py-1 pl-6"><span class="text-teal-400">Turtle</span> <span class="text-white">myTurtle</span>;</div>
                <div class="px-2 py-1 pl-6"><span class="text-white">myTurtle</span>.<span class="text-purple-400">penDown</span>();</div>
                <div class="px-2 py-1 pl-6"><span class="text-white">myTurtle</span>.<span class="text-purple-400">forward</span>(<span class="text-yellow-400">10</span>);</div>
                <div class="px-2 py-1 pl-6"><span class="text-white">myTurtle</span>.<span class="text-purple-400">penUp</span>();</div>
                <div class="px-2 py-1"><span class="text-white">}</span></div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>

    <script>
      const totalFrames = 150;
      const frameBasePath = '/public/v5_webp/turtleExplodev5';
      const textSections = [
        { id: 'textSection1', startFrame: 1,   endFrame: 50  },
        { id: 'textSection2', startFrame: 52,  endFrame: 102 },
        { id: 'textSection3', startFrame: 104, endFrame: 150 }
      ];

      let currentFrame = 1;
      let scrollProgress = 0;
      let targetProgress = 0;
      let animationComplete = false;
      let activeTextSection = null;
      const preloadedFrames = new Map();

      function preloadFrame(n) {
        if (n < 1 || n > totalFrames) return;
        if (!preloadedFrames.has(n)) {
          const img = new Image();
          img.src = `${frameBasePath}${String(n).padStart(5,'0')}.webp`;
          preloadedFrames.set(n, img);
        }
      }
      function preloadInitialFrames() {
        for (let i = 1; i <= Math.min(100, totalFrames); i++) preloadFrame(i);
      }
      function updateVideoFrame() {
        const frameNum = Math.floor(scrollProgress * (totalFrames - 1)) + 1;
        if (frameNum !== currentFrame) {
          currentFrame = frameNum;
          document.getElementById('videoFrame').src =
            `${frameBasePath}${String(frameNum).padStart(5,'0')}.webp`;
          for (let i = 1; i <= 30; i++) preloadFrame(frameNum + i);
        }
      }
      function showTextSection(s) {
        const el = document.getElementById(s.id);
        el.classList.remove('exit-left','exit-right');
        el.classList.add('active');
      }
      function hideTextSection(s) {
        const el = document.getElementById(s.id);
        el.classList.remove('active');
        el.classList.add(currentFrame > s.endFrame ? 'exit-right' : 'exit-left');
      }
      function updateTextSections() {
        const section = textSections.find(s =>
          currentFrame >= s.startFrame && currentFrame <= s.endFrame
        );
        if (section && (!activeTextSection || activeTextSection.id !== section.id)) {
          if (activeTextSection) hideTextSection(activeTextSection);
          showTextSection(section);
          activeTextSection = section;
        } else if (!section && activeTextSection) {
          hideTextSection(activeTextSection);
          activeTextSection = null;
        }
      }
      function updateProgressBar() {
        document.getElementById('progressBar').style.width =
          `${scrollProgress * 100}%`;
      }

      function completeAnimation() {
        if (animationComplete) return;
        animationComplete = true;

        // snap to final frame
        scrollProgress = targetProgress = 1;
        updateVideoFrame();
        updateProgressBar();
        updateTextSections();

        // show the post-animation content
        const postContent = document.querySelector('.post-animation-content');
        postContent.classList.add('visible');

        // scroll it into view, respecting scroll-margin-top
        postContent.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // re-enable page scrolling after the fade
        setTimeout(() => {
          document.body.style.overflow = 'auto';
        }, 600);
      }

      function reenterAnimation() {
        animationComplete = false;
        const postContent = document.querySelector('.post-animation-content');
        postContent.classList.remove('visible');
        document.body.style.overflow = 'hidden';
        const animationContainer = document.getElementById('animationContainer');
        const headerHeight = 80;
        const animationTop = animationContainer.offsetTop - headerHeight;
        window.scrollTo({ top: animationTop, behavior: 'smooth' });
      }

      const scrollSpeed = 0.0008;
      function handleScroll(deltaY) {
        if (!animationComplete) {
          let newProgress = targetProgress + deltaY * scrollSpeed;
          if (newProgress >= 1 && targetProgress >= 1) {
            completeAnimation();
            return;
          }
          targetProgress = Math.max(0, Math.min(1, newProgress));
        } else if (deltaY < 0) {
          const postContent = document.querySelector('.post-animation-content');
          const rect = postContent.getBoundingClientRect();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          if (rect.top >= 0 || scrollTop <= postContent.offsetTop) {
            reenterAnimation();
            targetProgress = scrollProgress = 0.95;
            updateVideoFrame();
            updateProgressBar();
            updateTextSections();
          }
        }
      }

      function animate() {
        scrollProgress += (targetProgress - scrollProgress) * 0.1;
        updateVideoFrame();
        updateProgressBar();
        updateTextSections();
        if (!animationComplete && scrollProgress >= 0.99) completeAnimation();
        requestAnimationFrame(animate);
      }

      function init() {
        document.body.style.overflow = 'hidden';
        preloadInitialFrames();
        requestAnimationFrame(animate);

        const onWheel = e => {
          if (!animationComplete) {
            e.preventDefault();
            handleScroll(e.deltaY);
          } else {
            handleScroll(e.deltaY);
          }
        };
        window.addEventListener('wheel', onWheel, { passive: false });

        const onKeydown = e => {
          if (!animationComplete) {
            if (e.key === 'ArrowDown' || e.key === 'PageDown') {
              e.preventDefault(); handleScroll(100);
            }
            if (e.key === 'ArrowUp' || e.key === 'PageUp') {
              e.preventDefault(); handleScroll(-100);
            }
          } else {
            if (e.key === 'ArrowUp' || e.key === 'PageUp') {
              const postContent = document.querySelector('.post-animation-content');
              const rect = postContent.getBoundingClientRect();
              const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              if (rect.top >= 0 || scrollTop <= postContent.offsetTop) {
                e.preventDefault(); handleScroll(-100);
              }
            }
          }
        };
        window.addEventListener('keydown', onKeydown);

        window.addEventListener('resize', () => {
          if (!animationComplete) {
            updateVideoFrame();
            updateProgressBar();
            updateTextSections();
          }
        });
      }
      window.addEventListener('load', init);
    </script>
  </body>
</html>

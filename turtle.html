<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
    <link rel="shortcut icon" href="/public/favicon.ico">
    <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Scroll Video - RISE STEM</title>
    <link href="/src/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/src/animations.css">
    <style>
      html {
        scroll-behavior: smooth;
      }

      body {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .main-content {
        flex: 1;
        padding-bottom: 2rem;
      }

      .scroll-video-container {
        position: fixed;
        top: 50%;
        right: 50px;
        transform: translateY(-50%);
        width: 400px;
        height: 300px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 100;
      }

      .scroll-video-frame {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.1s ease;
      }

      .content-section {
        padding: 40px 60px;
        max-width: 600px;
        margin-bottom: 2rem;
      }

      .content-section h1,
      .content-section h2 {
        margin-bottom: 1.5rem;
        line-height: 1.2;
      }

      .content-section p {
        margin-bottom: 1rem;
        line-height: 1.6;
      }

      .content-section p:last-child {
        margin-bottom: 0;
      }

      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        z-index: 200;
        transition: width 0.1s ease;
      }

      @media (max-width: 768px) {
        .scroll-video-container {
          width: 280px;
          height: 210px;
          right: 20px;
        }

        .content-section {
          padding: 30px 30px;
          max-width: calc(100% - 320px);
        }
      }

      @media (max-width: 640px) {
        .scroll-video-container {
          width: 240px;
          height: 180px;
          right: 10px;
        }

        .content-section {
          padding: 20px 20px;
          max-width: calc(100% - 260px);
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-['Poppins']">
    <!-- Header -->
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Fixed Video Container -->
    <div class="scroll-video-container">
      <img
        id="videoFrame"
        class="scroll-video-frame"
        src="public/v4_frames_web/turtleExplodev40001.png"
        alt="Video Frame"
      />
    </div>

    <!-- Main Content -->
    <div class="main-content pt-20">
      <!-- Section 1 -->
      <section class="content-section">
        <div class="fadeInUp-animation">
          <h1 class="text-4xl font-bold text-purple-950">Welcome to RISE STEM</h1>
          <p class="text-lg text-gray-700">
            As you scroll through this page, watch our interactive animation play alongside our story.
            This demonstrates how technology can create engaging educational experiences that inspire
            young minds to explore STEM fields.
          </p>
          <p class="text-lg text-gray-700">
            At RISE STEM, we believe in making science, technology, engineering, and mathematics
            accessible and exciting for all students. Our innovative approach combines hands-on
            learning with cutting-edge technology to create memorable educational experiences.
          </p>
          <p class="text-lg text-gray-700">
            Through our programs, workshops, and community initiatives, we're building the next
            generation of innovators, problem-solvers, and leaders who will shape our future.
          </p>
        </div>
      </section>

      <!-- Section 2 -->
      <section class="content-section">
        <div class="fadeInUp-animation">
          <h2 class="text-3xl font-bold text-purple-950">Our Educational Innovation</h2>
          <p class="text-lg text-gray-700">
            The animation continues to play as we explore our innovative teaching methods. Each frame
            represents our commitment to creating engaging, technology-enhanced learning experiences
            that make STEM subjects come alive for students.
          </p>
          <p class="text-lg text-gray-700">
            Our curriculum integrates real-world applications with fundamental concepts, ensuring
            students not only learn the theory but also understand how STEM principles apply to
            solving actual challenges in their communities and beyond.
          </p>
          <p class="text-lg text-gray-700">
            From robotics and coding to environmental science and engineering design, we provide
            comprehensive programs that nurture curiosity and develop critical thinking skills
            essential for success in the 21st century.
          </p>
        </div>
      </section>

      <!-- Section 3 -->
      <section class="content-section">
        <div class="fadeInUp-animation">
          <h2 class="text-3xl font-bold text-purple-950">Student Discovery & Growth</h2>
          <p class="text-lg text-gray-700">
            As we reach the heart of our mission, the animation reveals the transformative
            journey our students experience. Through hands-on projects and collaborative
            learning, students discover their potential and develop confidence in STEM fields.
          </p>
          <p class="text-lg text-gray-700">
            Our students work on real projects that matter to their communities, from designing
            sustainable solutions to environmental challenges to creating apps that solve
            everyday problems. This practical approach ensures learning is both meaningful and memorable.
          </p>
          <p class="text-lg text-gray-700">
            We celebrate every breakthrough moment, from a student's first successful code
            compilation to their innovative engineering design. These victories, big and small,
            build the foundation for lifelong learning and achievement.
          </p>
        </div>
      </section>

      <!-- Section 4 -->
      <section class="content-section">
        <div class="fadeInUp-animation">
          <h2 class="text-3xl font-bold text-purple-950">Community Impact & Transformation</h2>
          <p class="text-lg text-gray-700">
            The transformation continues as our students become leaders in their communities.
            Each scroll through our story represents the ripple effect of quality STEM education,
            creating positive change that extends far beyond the classroom.
          </p>
          <p class="text-lg text-gray-700">
            Our alumni go on to pursue careers in technology, engineering, medicine, and research,
            but more importantly, they carry with them the problem-solving mindset and innovative
            thinking that RISE STEM has helped cultivate.
          </p>
          <p class="text-lg text-gray-700">
            Through partnerships with local schools, community organizations, and industry leaders,
            we're building a network of support that ensures every student has access to quality
            STEM education and the opportunity to reach their full potential.
          </p>
        </div>
      </section>

      <!-- Section 5 -->
      <section class="content-section">
        <div class="fadeInUp-animation">
          <h2 class="text-3xl font-bold text-purple-950">Looking Forward</h2>
          <p class="text-lg text-gray-700">
            As we reach the end of our scroll journey, the video animation completes its
            sequence, bringing our story full circle with a satisfying conclusion.
          </p>
          <p class="text-lg text-gray-700">
            The future of STEM education is bright, and we're excited to continue pushing
            the boundaries of what's possible when innovative teaching meets passionate learning.
          </p>
          <p class="text-lg text-gray-700">
            Join us in this mission to inspire, educate, and empower the next generation
            of STEM leaders who will tackle tomorrow's greatest challenges.
          </p>
        </div>
      </section>
    </div>

    <!-- Footer Section -->
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>

    <script>
      // Configuration
      const totalFrames = 150;
      const frameBasePath = 'public/v4_frames_web/turtleExplodev4';

      // Get elements
      const videoFrame = document.getElementById('videoFrame');
      const progressBar = document.getElementById('progressBar');

      // Preload some frames for smoother experience
      const preloadedFrames = new Map();

      function preloadFrame(frameNumber) {
        if (!preloadedFrames.has(frameNumber)) {
          const img = new Image();
          const paddedNumber = frameNumber.toString().padStart(4, '0');
          img.src = `${frameBasePath}${paddedNumber}.png`;
          preloadedFrames.set(frameNumber, img);
        }
        return preloadedFrames.get(frameNumber);
      }

      // Preload first few frames
      for (let i = 1; i <= Math.min(10, totalFrames); i++) {
        preloadFrame(i);
      }

      function updateVideoFrame() {
        // Calculate scroll progress (0 to 1)
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollProgress = Math.min(scrollTop / scrollHeight, 1);

        // Calculate frame number based on scroll progress
        const frameNumber = Math.max(1, Math.min(totalFrames, Math.floor(scrollProgress * totalFrames) + 1));

        // Update progress bar
        progressBar.style.width = `${scrollProgress * 100}%`;

        // Update video frame
        const paddedNumber = frameNumber.toString().padStart(4, '0');
        const newSrc = `${frameBasePath}${paddedNumber}.png`;

        if (videoFrame.src !== newSrc) {
          videoFrame.src = newSrc;

          // Preload next few frames
          for (let i = 1; i <= 3; i++) {
            const nextFrame = frameNumber + i;
            if (nextFrame <= totalFrames) {
              preloadFrame(nextFrame);
            }
          }
        }
      }

      // Throttle scroll events for better performance
      let ticking = false;

      function handleScroll() {
        if (!ticking) {
          requestAnimationFrame(() => {
            updateVideoFrame();
            ticking = false;
          });
          ticking = true;
        }
      }

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Initialize on page load
      window.addEventListener('load', updateVideoFrame);

      // Handle window resize
      window.addEventListener('resize', updateVideoFrame);
    </script>
  </body>
</html>
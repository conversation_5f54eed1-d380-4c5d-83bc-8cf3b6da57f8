<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
    <link rel="shortcut icon" href="/public/favicon.ico">
    <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Scroll Video - RISE STEM</title>
    <link href="/src/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/src/animations.css">
    <style>
      html {
        scroll-behavior: smooth;
      }

      .scroll-video-container {
        position: fixed;
        top: 50%;
        right: 50px;
        transform: translateY(-50%);
        width: 400px;
        height: 300px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 100;
      }

      .scroll-video-frame {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.1s ease;
      }

      .content-section {
        min-height: 100vh;
        padding: 80px 60px;
        max-width: 600px;
      }

      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        z-index: 200;
        transition: width 0.1s ease;
      }

      @media (max-width: 768px) {
        .scroll-video-container {
          width: 280px;
          height: 210px;
          right: 20px;
        }

        .content-section {
          padding: 60px 30px;
          max-width: calc(100% - 320px);
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-['Poppins']">
    <!-- Header -->
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Fixed Video Container -->
    <div class="scroll-video-container">
      <img
        id="videoFrame"
        class="scroll-video-frame"
        src="public/v4_frames/turtleExplodev40001.png"
        alt="Video Frame"
      />
    </div>

    <!-- Main Content -->
    <div class="min-h-screen pt-20">
      <!-- Section 1 -->
      <section class="content-section">
        <div class="bg-white fadeInUp-animation p-8 rounded-2xl border border-gray-300 shadow-md">
          <h1 class="text-4xl font-bold text-purple-950 mb-6">Welcome to RISE STEM</h1>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            As you scroll through this page, watch our interactive animation play alongside our story.
            This demonstrates how technology can create engaging educational experiences that inspire
            young minds to explore STEM fields.
          </p>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            At RISE STEM, we believe in making science, technology, engineering, and mathematics
            accessible and exciting for all students. Our innovative approach combines hands-on
            learning with cutting-edge technology to create memorable educational experiences.
          </p>
          <p class="text-lg text-gray-600 leading-relaxed">
            Through our programs, workshops, and community initiatives, we're building the next
            generation of innovators, problem-solvers, and leaders who will shape our future.
          </p>
        </div>
      </section>

      <!-- Section 2 -->
      <section class="content-section">
        <div class="bg-white fadeInUp-animation p-8 rounded-2xl border border-gray-300 shadow-md">
          <h2 class="text-3xl font-bold text-purple-950 mb-6">Our Educational Innovation</h2>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            The animation continues to play as we explore our innovative teaching methods. Each frame
            represents our commitment to creating engaging, technology-enhanced learning experiences
            that make STEM subjects come alive for students.
          </p>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            Our curriculum integrates real-world applications with fundamental concepts, ensuring
            students not only learn the theory but also understand how STEM principles apply to
            solving actual challenges in their communities and beyond.
          </p>
          <p class="text-lg text-gray-600 leading-relaxed">
            From robotics and coding to environmental science and engineering design, we provide
            comprehensive programs that nurture curiosity and develop critical thinking skills
            essential for success in the 21st century.
          </p>
        </div>
      </section>

      <!-- Section 3 -->
      <section class="content-section">
        <div class="bg-white fadeInUp-animation p-8 rounded-2xl border border-gray-300 shadow-md">
          <h2 class="text-3xl font-bold text-purple-950 mb-6">Student Discovery & Growth</h2>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            As we reach the heart of our mission, the animation reveals the transformative
            journey our students experience. Through hands-on projects and collaborative
            learning, students discover their potential and develop confidence in STEM fields.
          </p>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            Our students work on real projects that matter to their communities, from designing
            sustainable solutions to environmental challenges to creating apps that solve
            everyday problems. This practical approach ensures learning is both meaningful and memorable.
          </p>
          <p class="text-lg text-gray-600 leading-relaxed">
            We celebrate every breakthrough moment, from a student's first successful code
            compilation to their innovative engineering design. These victories, big and small,
            build the foundation for lifelong learning and achievement.
          </p>
        </div>
      </section>

      <!-- Section 4 -->
      <section class="content-section">
        <div class="bg-white fadeInUp-animation p-8 rounded-2xl border border-gray-300 shadow-md">
          <h2 class="text-3xl font-bold text-purple-950 mb-6">Community Impact & Transformation</h2>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            The transformation continues as our students become leaders in their communities.
            Each scroll through our story represents the ripple effect of quality STEM education,
            creating positive change that extends far beyond the classroom.
          </p>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            Our alumni go on to pursue careers in technology, engineering, medicine, and research,
            but more importantly, they carry with them the problem-solving mindset and innovative
            thinking that RISE STEM has helped cultivate.
          </p>
          <p class="text-lg text-gray-600 leading-relaxed">
            Through partnerships with local schools, community organizations, and industry leaders,
            we're building a network of support that ensures every student has access to quality
            STEM education and the opportunity to reach their full potential.
          </p>
        </div>
      </section>

      <!-- Section 5 -->
      <section class="content-section bg-gray-50">
        <h2 class="text-3xl font-bold text-gray-800 mb-6">Chapter Five: Conclusion</h2>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
          As we reach the end of our scroll journey, the video animation completes its
          sequence, bringing our story full circle with a satisfying conclusion.
        </p>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
          Excepturi sint occaecati cupiditate non provident, similique sunt in culpa
          qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
        </p>
        <p class="text-lg text-gray-600 leading-relaxed">
          Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore,
          cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod
          maxime placeat facere possimus.
        </p>
      </section>
    </div>

    <!-- Footer Section -->
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>

    <script>
      // Configuration
      const totalFrames = 150;
      const frameBasePath = 'public/v3_frames/frame_';

      // Get elements
      const videoFrame = document.getElementById('videoFrame');
      const progressBar = document.getElementById('progressBar');

      // Preload some frames for smoother experience
      const preloadedFrames = new Map();

      function preloadFrame(frameNumber) {
        if (!preloadedFrames.has(frameNumber)) {
          const img = new Image();
          const paddedNumber = frameNumber.toString().padStart(4, '0');
          img.src = `${frameBasePath}${paddedNumber}.png`;
          preloadedFrames.set(frameNumber, img);
        }
        return preloadedFrames.get(frameNumber);
      }

      // Preload first few frames
      for (let i = 1; i <= Math.min(10, totalFrames); i++) {
        preloadFrame(i);
      }

      function updateVideoFrame() {
        // Calculate scroll progress (0 to 1)
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollProgress = Math.min(scrollTop / scrollHeight, 1);

        // Calculate frame number based on scroll progress
        const frameNumber = Math.max(1, Math.min(totalFrames, Math.floor(scrollProgress * totalFrames) + 1));

        // Update progress bar
        progressBar.style.width = `${scrollProgress * 100}%`;

        // Update video frame
        const paddedNumber = frameNumber.toString().padStart(4, '0');
        const newSrc = `${frameBasePath}${paddedNumber}.png`;

        if (videoFrame.src !== newSrc) {
          videoFrame.src = newSrc;

          // Preload next few frames
          for (let i = 1; i <= 3; i++) {
            const nextFrame = frameNumber + i;
            if (nextFrame <= totalFrames) {
              preloadFrame(nextFrame);
            }
          }
        }
      }

      // Throttle scroll events for better performance
      let ticking = false;

      function handleScroll() {
        if (!ticking) {
          requestAnimationFrame(() => {
            updateVideoFrame();
            ticking = false;
          });
          ticking = true;
        }
      }

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Initialize on page load
      window.addEventListener('load', updateVideoFrame);

      // Handle window resize
      window.addEventListener('resize', updateVideoFrame);
    </script>
  </body>
</html>
<!doctype html>
<html lang="en">
  <head>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Scroll Video - RISE STEM</title>
    <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
    <link rel="icon" type="image/png" href="/public/favicon-32x32.png" />
    <link rel="shortcut icon" href="/public/favicon.ico" />
    <link rel="apple-touch-icon" href="/public/apple-touch-icon.png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <style>
      html { scroll-behavior: smooth; }
      body { margin: 0; padding: 0; font-family: 'Poppins', sans-serif; background-color: #f9fafb; }

      /* Container & Layout */
      .animation-container { height: 100vh; overflow: hidden; position: relative; background: #f9fafb; display: flex; align-items: center; justify-content: center; }
      .scroll-video-container { position: absolute; right: 5%; top: 50%; transform: translateY(-50%); width: 60%; height: 80%; max-width: none; z-index: 10; display: flex; align-items: center; justify-content: center; }
      .scroll-video-frame { max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; }
      .scroll-text-container { position: absolute; left: 5%; top: 50%; transform: translateY(-50%); width: 30%; max-width: 400px; z-index: 20; height: 200px; }

      /* Text sections */
      .scroll-text-section { position: absolute; width: 100%; top: 0; left: 0; opacity: 0; transform: translateX(-100px); transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); pointer-events: none; }
      .scroll-text-section.active { opacity: 1; transform: translateX(0); pointer-events: auto; }
      .scroll-text-section.exit-right { opacity: 0; transform: translateX(100px); }
      .scroll-text-section.exit-left  { opacity: 0; transform: translateX(-100px); }

      .scroll-text-section h2 { font-size: 2rem; font-weight: bold; color: #581c87; margin-bottom: 1.5rem; line-height: 1.2; }
      .scroll-text-section p  { font-size: 1.1rem; color: #374151; line-height: 1.6; margin-bottom: 1rem; }

      /* Progress Bar */
      .progress-bar { position: fixed; top: 0; left: 0; height: 4px; background: linear-gradient(90deg, #3b82f6, #8b5cf6); z-index: 100; transition: width 0.2s ease; }

      /* Post-animation */
      .post-animation-content { min-height: 100vh; padding: 4rem 2rem; max-width: 1200px; margin: 2rem auto 0; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
      .post-animation-content h1 { font-size: 3rem; font-weight: bold; color: #581c87; margin-bottom: 2rem; text-align: center; }
      .post-animation-content h2 { font-size: 2rem; font-weight: bold; color: #581c87; margin: 3rem 0 1.5rem; }
      .post-animation-content p  { font-size: 1.1rem; color: #374151; line-height: 1.7; margin-bottom: 1.5rem; }

      /* Responsive */
      @media (max-width: 768px) {
        .scroll-video-container { width: 90%; height: 70vh; right: 5%; top: 15%; }
        .scroll-text-container  { width: 90%; left: 5%; top: 85%; }
        .scroll-text-section h2  { font-size: 1.5rem; }
        .scroll-text-section p   { font-size: 1rem; }
      }
      @media (max-width: 640px) {
        .animation-container  { flex-direction: column; padding: 2rem 1rem; }
        .scroll-video-container { position: relative; width: 100%; height: 60vh; right: auto; top: auto; transform: none; margin-bottom: 2rem; }
        .scroll-text-container   { position: relative; width: 100%; left: auto; top: auto; transform: none; }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <div id="header-container"></div>
    <script type="module" src="/scripts/loadHeader.js"></script>
    
    <div class="progress-bar" id="progressBar"></div>

    <div class="animation-container" id="animationContainer">
      <!-- White Content Box following RISE design pattern -->
      <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 py-8 h-full flex items-center justify-center">
        <div class="bg-white rounded-2xl border border-gray-300 shadow-md p-8 w-full h-full relative">
          <div class="scroll-video-container">
            <img
              id="videoFrame"
              class="scroll-video-frame"
              src="public/v5_webp/turtleExplodev500001.webp"
              alt="Video Frame"
            />
          </div>
          <div class="scroll-text-container">
        <div class="scroll-text-section" id="textSection1">
          <h2>Innovation in Learning</h2>
          <p>Discover how cutting-edge tech transforms traditional education into immersive, interactive experiences.</p>
        </div>
        <div class="scroll-text-section" id="textSection2">
          <h2>Student Engagement</h2>
          <p>Our hands-on approach ensures every student actively participates, building confidence and practical skills.</p>
        </div>
        <div class="scroll-text-section" id="textSection3">
          <h2>Scientific Discovery</h2>
          <p>Through experimentation, students uncover the wonders of science and develop critical thinking abilities.</p>
        </div>
        </div> <!-- Close white content box -->
      </div> <!-- Close padding container -->
    </div>

    <!-- Post-Animation Content with white box design -->
    <div class="w-full px-6 sm:px-6 md:px-16 lg:px-24 py-8 bg-gray-50">
      <div class="bg-white rounded-2xl border border-gray-300 shadow-md p-8">
        <h1 class="text-4xl font-bold text-purple-950 mb-6 text-center">Discover More About RISE STEM</h1>

        <h2 class="text-3xl font-bold text-purple-950 mb-4">Our Mission</h2>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
          At RISE STEM, we believe in making science, technology, engineering, and mathematics accessible and exciting for all students. Our innovative approach combines hands-on learning with cutting-edge technology to create memorable educational experiences.
        </p>

        <h2 class="text-3xl font-bold text-purple-950 mb-4">Interactive Learning</h2>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
          Through interactive animations like the one you just experienced, we demonstrate how technology can transform traditional education into engaging, immersive experiences that inspire young minds to explore STEM fields.
        </p>

        <h2 class="text-3xl font-bold text-purple-950 mb-4">Join Our Community</h2>
        <p class="text-lg text-gray-600 leading-relaxed">
          Whether you're a student eager to explore STEM, a parent looking for quality educational opportunities, or a community member wanting to support youth development, there's a place for you in our growing RISE STEM family.
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>
    <script type="module" src="/scripts/loadFooter.js"></script>

    <script>
      const totalFrames = 150;
      const frameBasePath = 'public/v5_webp/turtleExplodev5';
      const textSections = [
        { id: 'textSection1', startFrame:   1, endFrame:  50 },
        { id: 'textSection2', startFrame:  52, endFrame:  102 },
        { id: 'textSection3', startFrame:  104, endFrame:  150 }
      ];

      let currentFrame = 1;
      let scrollProgress = 0;
      let targetProgress = 0;
      let animationComplete = false;
      let activeTextSection = null;
      const preloadedFrames = new Map();

      function preloadFrame(n) {
        if (n < 1 || n > totalFrames) return;
        if (!preloadedFrames.has(n)) {
          const img = new Image();
          img.src = `${frameBasePath}${String(n).padStart(5,'0')}.webp`;
          preloadedFrames.set(n, img);
        }
      }
      function preloadInitialFrames() {
        for (let i = 1; i <= Math.min(50, totalFrames); i++) preloadFrame(i);
      }

      function updateVideoFrame() {
        const frameNum = Math.floor(scrollProgress * (totalFrames - 1)) + 1;
        if (frameNum !== currentFrame) {
          currentFrame = frameNum;
          document.getElementById('videoFrame').src =
            `${frameBasePath}${String(frameNum).padStart(5,'0')}.webp`;
          for (let i = 1; i <= 10; i++) preloadFrame(frameNum + i);
        }
      }

      function updateTextSections() {
        const section = textSections.find(
          s => currentFrame >= s.startFrame && currentFrame <= s.endFrame
        );
        if (section && (!activeTextSection || activeTextSection.id !== section.id)) {
          if (activeTextSection) hideTextSection(activeTextSection);
          showTextSection(section);
          activeTextSection = section;
        } else if (!section && activeTextSection) {
          hideTextSection(activeTextSection);
          activeTextSection = null;
        }
      }

      function showTextSection(s) {
        const el = document.getElementById(s.id);
        el.classList.remove('exit-left','exit-right');
        el.classList.add('active');
      }
      function hideTextSection(s) {
        const el = document.getElementById(s.id);
        el.classList.remove('active');
        el.classList.add(
          currentFrame > s.endFrame ? 'exit-right' : 'exit-left'
        );
      }

      function updateProgressBar() {
        document.getElementById('progressBar').style.width =
          `${scrollProgress * 100}%`;
      }

      const scrollSpeed = 0.0008; // tuning
      function handleScroll(deltaY) {
        targetProgress = Math.max(0, Math.min(1,
          targetProgress + deltaY * scrollSpeed
        ));
      }

      function animate() {
        scrollProgress += (targetProgress - scrollProgress) * 0.1;
        updateVideoFrame();
        updateProgressBar();
        updateTextSections();
        if (!animationComplete && scrollProgress >= 1) {
          completeAnimation();
        }
        requestAnimationFrame(animate);
      }

      function completeAnimation() {
        animationComplete = true;
        document.body.style.overflow = 'auto';
        document.querySelector('.post-animation-content')
          .scrollIntoView({ behavior: 'smooth' });
      }

      function init() {
        document.body.style.overflow = 'hidden';
        preloadInitialFrames();
        requestAnimationFrame(animate);
        window.addEventListener('wheel', e => {
          e.preventDefault();
          handleScroll(e.deltaY);
        }, { passive: false });
        window.addEventListener('keydown', e => {
          if (e.key === 'ArrowDown' || e.key === 'PageDown') {
            e.preventDefault();
            handleScroll(100);
          }
          if (e.key === 'ArrowUp' || e.key === 'PageUp') {
            e.preventDefault();
            handleScroll(-100);
          }
        });
      }

      window.addEventListener('load', init);
      window.addEventListener('resize', () => {
        if (!animationComplete) {
          updateVideoFrame();
          updateProgressBar();
          updateTextSections();
        }
      });
    </script>
  </body>
</html>
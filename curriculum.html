<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/public/logo1.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Curriculum - RISE STEM</title>
  <link href="/src/style.css" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html { scroll-behavior: smooth; }
    .section-content { display: none; }
    .section-content.active { display: block; }
    .tab-button.active { background-color: #4a1d96; color: white; }
  </style>
</head>
<body>
<!-- Header/Navigation -->
<div id="header-container"></div>
<script type="module" src="/scripts/loadHeader.js"></script>

<!-- Main content -->
<section class="min-h-screen bg-gray-50 w-full pt-16">
  <!-- Hero Banner -->
  <div class="w-full h-[60vh] bg-[url('/public/number_of_kids_doing_reaction.jpeg')] bg-cover bg-[center_70%] bg-no-repeat flex items-center justify-center relative">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="flex flex-col text-center items-center justify-center px-6 sm:px-16 md:px-32 z-10">
      <h1 class="mt-16 text-3xl font-extrabold text-white md:text-4xl xl:text-5xl justify-center">
        Curriculum
      </h1>
    </div>
  </div>

  <section class="bg-gray-50">
    <div class="container mx-auto px-6 flex flex-col lg:flex-row">

      <!-- Left Side Navigation -->
      <div id="curriculum-nav" class="-mt-6 w-full lg:w-1/3 hidden lg:block px-12"></div>

      <!-- Main Content -->
      <div class="w-full lg:w-3/5 py-6">
        <!-- Breadcrumb Navigation -->
        <div class="flex items-center text-lg font-normal mb-8 text-purple-950">
          <a href="/home" class="hover:underline">Home</a>
          <span class="mx-4">›</span>
          <a href="/curriculum" class="hover:underline">Curriculum</a>
          <span id="breadcrumb-separator" class="mx-4 hidden">›</span>
          <span id="current-section" class="font-bold hidden"></span>
        </div>

        <!-- Section Tabs (Mobile & Tablet) -->
        <div id="curriculum-tabs" class="flex mb-6 lg:hidden"></div>

        <!-- Welcome -->
        <div id="welcome-content" class="section-content active">
          <h1 class="text-3xl md:text-5xl font-extrabold text-purple-950 mb-8">Curriculum</h1>
          <p class="text-lg mb-12 text-gray-500">
            Welcome to the Rise Engineering & Coding curriculum page. Please select a subject from the tabs above to explore our curriculum materials.
          </p>
          <div id="welcome-grid" class="grid grid-cols-1 md:grid-cols-2 gap-8"></div>
        </div>

        <!-- Dynamic curriculum content will be injected here -->
        <div id="curriculum-sections"></div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
</section>

<script>
async function loadCurriculum() {
  try {
    const res = await fetch('/curriculum.json');
    if (!res.ok) throw new Error('Could not load curriculum.json');
    return await res.json();
  } catch (e) {
    console.error('Error loading curriculum.json:', e);
    return null;
  }
}

function createNav(sections) {
  const nav = document.createElement('div');
  nav.className = "bg-purple-950 pt-12 min-h-full text-white px-8 p-6 rounded-lg sticky top-20";
  nav.innerHTML = '<h2 class="text-2xl font-bold mb-6">In This Section</h2>';
  const navList = document.createElement('nav');
  navList.className = "space-y-4";
  sections.forEach(section => {
    const btnId = `nav-${section.id}-btn`;
    const subnavId = `${section.id}-subnav`;
    const iconId = `${section.id}-icon`;
    const btn = document.createElement('button');
    btn.id = btnId;
    btn.className = "block font-semibold text-lg w-full text-left transition-colors duration-300 hover:text-gray-300 flex justify-between items-center";
    btn.innerHTML = `${section.name} <span id="${iconId}">+</span>`;

    const subnav = document.createElement('nav');
    subnav.id = subnavId;
    subnav.className = "space-y-2 pl-4 hidden";
    section.days.forEach(day => {
      const a = document.createElement('a');
      a.href = `#${section.id}-day${day.id.replace('day', '')}`;
      a.className = "block text-md text-white hover:text-gray-300 transition-colors duration-300";
      a.textContent = day.title;
      subnav.appendChild(a);
    });
    navList.appendChild(btn);
    navList.appendChild(subnav);
  });
  nav.appendChild(navList);
  return nav;
}

function createTabs(sections) {
  const tabs = document.createElement('div');
  tabs.className = "flex mb-6 lg:hidden";
  sections.forEach((section) => {
    const tab = document.createElement('button');
    tab.id = `${section.id}-tab`;
    tab.className = `tab-button flex-1 py-3 px-4 font-bold text-center rounded border border-purple-950`;
    tab.textContent = section.name;
    tabs.appendChild(tab);
  });
  return tabs;
}

function createWelcomeGrid(sections) {
  const grid = document.createElement('div');
  grid.className = "grid grid-cols-1 md:grid-cols-2 gap-8";
  sections.forEach(section => {
    const card = document.createElement('div');
    card.className = "border p-6 rounded-lg shadow-md bg-gray-50 flex flex-col z-30 h-full";
    card.innerHTML = `
      <h2 class="text-2xl font-bold text-purple-950 mb-4">${section.name}</h2>
      <p class="text-gray-500 mb-4">
        ${
          section.welcome_description?.trim()
            ? section.welcome_description
            : section.description?.trim()
              ? section.description
              : section.id === 'coding'
                ? 'Our Python coding curriculum introduces students to programming concepts through interactive lessons and Pygame projects.'
                : 'Explore various engineering disciplines through hands-on projects and engaging activities.'
        }
      </p>
      <div class="flex-grow"></div>
      <button id="view-${section.id}-btn" class="mt-4 px-6 py-2 bg-purple-950 text-white rounded-md transform transition-all duration-300 hover:scale-110">
        View Curriculum
      </button>
    `;
    grid.appendChild(card);
  });
  return grid;
}

function createSectionContent(sections) {
  const container = document.createElement('div');
  sections.forEach((section) => {
    const secDiv = document.createElement('div');
    secDiv.id = `${section.id}-content`;
    secDiv.className = `section-content`;
    secDiv.innerHTML = `
      <h1 class="text-3xl md:text-5xl font-extrabold bg-gray-50 text-purple-950 mb-8">${section.name} Curriculum</h1>
      <p class="text-lg mb-12 text-gray-500">
        ${section.description || (
          section.id === 'coding'
          ? 'Our curriculum is intended to introduce students to the Python coding language through the free Pygame library. Pygame is a set of Python modules designed for writing games. Our goal is to inspire a passion for computer science through fun and engaging lessons.'
          : 'Our curriculum is intended to introduce students to the field of engineering. By exploring different types of engineering, students will be exposed to what engineers do, how to solve real-world problems, and will engage in projects that represent their learning.'
        )}
      </p>
      <div class="space-y-4">
        ${section.days.map(day => `
          <div id="${section.id}-day${day.id.replace('day', '')}" class="border bg-gray-200 rounded">
            <button class="accordion-btn flex justify-between items-center w-full p-4 text-left text-purple-950 font-bold text-xl focus:outline-none">
              ${day.title}
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
            </button>
            <div class="accordion-content hidden p-4 bg-white">
              ${day.pdf ? `<iframe src="${day.pdf}" class="w-full h-screen" frameborder="0"></iframe>` : `<p>Coming Soon...</p>`}
              ${(day.pdf || day.ppt) ? `
              <div class="mt-6 border border-gray-300 rounded-lg">
                ${day.pdf ? `
                <div class="flex items-center justify-between p-4 border-b border-gray-300">
                  <span class="text-lg text-gray-500">${day.title} Curriculum.pdf</span>
                  <a href="${day.pdf}" target="_blank"><button class="px-6 py-2 bg-white border border-gray-300 text-gray-500 rounded-lg hover:bg-gray-100 transition">Download</button></a>
                </div>` : ''}
                ${day.ppt ? `
                <div class="flex items-center justify-between p-4">
                  <span class="text-lg text-gray-500">${day.title} Slideshow.pptx</span>
                  <a href="${day.ppt}" download><button class="px-6 py-2 bg-white border border-gray-300 text-gray-500 rounded-lg hover:bg-gray-100 transition">Download</button></a>
                </div>` : ''}
              </div>
              ` : ''}
            </div>
          </div>
        `).join('')}
      </div>
    `;
    container.appendChild(secDiv);
  });
  return container;
}

// Main loader
loadCurriculum().then(curriculum => {
  if (!curriculum || !curriculum.sections || !Array.isArray(curriculum.sections)) {
    document.getElementById('welcome-content').innerHTML += `<div class="mt-12 text-center text-red-600">Failed to load curriculum. Please check curriculum.json.</div>`;
    return;
  }

  // Navigation (sidebar)
  document.getElementById('curriculum-nav').appendChild(createNav(curriculum.sections));

  // Tabs (mobile/tablet)
  document.getElementById('curriculum-tabs').appendChild(createTabs(curriculum.sections));

  // Welcome grid
  const welcomeGrid = createWelcomeGrid(curriculum.sections);
  const oldGrid = document.getElementById('welcome-grid');
  if (oldGrid) oldGrid.replaceWith(welcomeGrid);

  // Section content
  const curriculumSections = createSectionContent(curriculum.sections);
  const oldSections = document.getElementById('curriculum-sections');
  if (oldSections) oldSections.replaceWith(curriculumSections);

  // Accordion JS
  document.querySelectorAll('.accordion-btn').forEach(button => {
    button.addEventListener('click', () => {
      const content = button.nextElementSibling;
      content.classList.toggle('hidden');
      const svg = button.querySelector('svg');
      svg.innerHTML = content.classList.contains('hidden')
        ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>'
        : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>';
    });
  });

  // Tab and nav logic
  curriculum.sections.forEach((section) => {
    // Tabs
    const tab = document.getElementById(`${section.id}-tab`);
    if (tab) {
      tab.addEventListener('click', () => {
        document.querySelectorAll('.tab-button').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        document.querySelectorAll('.section-content').forEach(sec => sec.classList.remove('active'));
        document.getElementById('welcome-content').classList.remove('active');
        document.getElementById(`${section.id}-content`).classList.add('active');
        document.getElementById('breadcrumb-separator').classList.remove('hidden');
        document.getElementById('current-section').classList.remove('hidden');
        document.getElementById('current-section').textContent = section.name;
      });
    }
    // Welcome buttons
    const viewBtn = document.getElementById(`view-${section.id}-btn`);
    if (viewBtn) {
      viewBtn.addEventListener('click', () => {
        if (tab) tab.click();
      });
    }
    // Sidebar nav
    const navBtn = document.getElementById(`nav-${section.id}-btn`);
    const subnav = document.getElementById(`${section.id}-subnav`);
    const icon = document.getElementById(`${section.id}-icon`);
    if (navBtn && subnav && icon) {
      navBtn.addEventListener('click', () => {
        subnav.classList.toggle('hidden');
        icon.textContent = icon.textContent === '+' ? '-' : '+';
      });
    }
  });
});
</script>
</body>
</html>

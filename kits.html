<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Kits Overview – RISE STEM</title>
  <link rel="icon" href="/public/favicon.ico"/>
  <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
  <link rel="shortcut icon" href="/public/favicon.ico">
  <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">

  <!-- Tailwind + font -->
  <script defer src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet"
  />

  <!-- Your custom CSS -->
  <link href="/src/style.css" rel="stylesheet">
  <link href="/src/animations.css" rel="stylesheet">

  <style>
    html { scroll-behavior: smooth; }
  </style>
</head>

<body class="flex flex-col min-h-screen font-poppins bg-gray-50">

  <!-- Header -->
  <div id="header-container"></div>
  <script type="module" src="/scripts/loadHeader.js"></script>

  <!-- Banner -->
  <section class="w-full flex items-start justify-center px-6 pt-32 bg-gray-50">
    <div class="flex justify-between items-center w-full max-w-screen-xl">
      <!-- Left Title -->
      <h1 class="text-[72px] font-extrabold text-gray-900 leading-none">
        RISE Kits
      </h1>

      <!-- Right Slogan -->
      <div class="flex flex-col justify-between text-[24px] leading-[1.2] text-gray-700 text-right h-[72px]">
        <span>Empowering tomorrow's engineers,</span>
        <span>one kit at a time.</span>
      </div>
    </div>
  </section>

  <!-- Get to Know RISE Kits Section -->
  <section class="w-full bg-white py-16">
    <div class="container mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-purple-950 mb-4">Get to Know RISE Kits</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to know about our educational STEM kits and how to get started.</p>
      </div>

      <!-- Subsections Grid -->
      <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">

        <!-- What are RISE Kits -->
        <div class="bg-gray-50 rounded-2xl p-8 border border-gray-200 shadow-sm">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-purple-950 rounded-lg flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-purple-950">What are RISE Kits?</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-4">
            RISE Kits are hands-on educational STEM kits designed to bridge the gap between digital learning and real-world application. Each kit combines 3D-printed components, off-the-shelf parts, and comprehensive programming guides to create engaging learning experiences.
          </p>
          <p class="text-gray-600 leading-relaxed mb-6">
            From drawing robots to electronic circuits, our kits teach fundamental engineering and programming concepts through practical, project-based learning that students can see, touch, and interact with.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-center">
              <span class="w-2 h-2 bg-purple-950 rounded-full mr-3"></span>
              Hands-on building and assembly
            </li>
            <li class="flex items-center">
              <span class="w-2 h-2 bg-purple-950 rounded-full mr-3"></span>
              Programming and coding integration
            </li>
            <li class="flex items-center">
              <span class="w-2 h-2 bg-purple-950 rounded-full mr-3"></span>
              Real-world engineering concepts
            </li>
            <li class="flex items-center">
              <span class="w-2 h-2 bg-purple-950 rounded-full mr-3"></span>
              Comprehensive learning materials
            </li>
          </ul>
        </div>

        <!-- Where can I get RISE Kits -->
        <div class="bg-gray-50 rounded-2xl p-8 border border-gray-200 shadow-sm">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-purple-950 rounded-lg flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-purple-950">Where can I get RISE Kits?</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            RISE Kits are available <strong>for free</strong> through multiple channels to ensure accessibility for educators, students, and families. We offer flexible options to meet different needs and budgets.
          </p>

          <div class="space-y-4 mb-6">
            <div class="border-l-4 border-purple-950 pl-4">
              <h4 class="font-semibold text-gray-800 mb-1">Workshop Programs</h4>
              <p class="text-gray-600 text-sm">We host free workshops where mentors provide Turtle Kits and guide participants through building and coding their own robots. Each attendee gets to take their kit home to keep exploring and creating. Learn more about workshops here.</p>
            </div>
            <div class="border-l-4 border-purple-950 pl-4">
              <h4 class="font-semibold text-gray-800 mb-1">Educational Institutions</h4>
              <p class="text-gray-600 text-sm">We partner with schools and educational organizations to provide RISE Kits for use in classrooms, clubs, and enrichment programs. If you're a teacher or administrator interested in bringing hands-on robotics to your students, contact us here.</p>
            </div>
            <div class="border-l-4 border-purple-950 pl-4">
              <h4 class="font-semibold text-gray-800 mb-1">Distribution Partners</h4>
              <p class="text-gray-600 text-sm">In the future, we plan to set up local distribution centers where students can pick up their own RISE Kits. Stay tuned for updates.</p>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-3">
            <a href="mailto:<EMAIL>?subject=RISE Kits Inquiry - Teacher/Administrator" class="flex-1 bg-purple-950 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-800 transition-colors text-center">
              Contact Us
            </a>
            <a href="/workshops" class="flex-1 border-2 border-purple-950 text-purple-950 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors text-center">
              Learn More About Workshops
            </a>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Main content -->
  <main id="kits-overview" class="flex-grow w-full bg-gray-50 py-16">
    <div class="container mx-auto px-6">
      <!-- Collection Header -->
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">Browse the collection.</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">Explore our range of STEM kits designed to inspire and educate the next generation of engineers and creators.</p>
      </div>

      <!-- Filter Pills -->
      <div class="flex flex-wrap justify-center gap-3 mb-16 px-4">
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="all">All Kits</button>
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="coding">Coding</button>
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="robotics">Robotics</button>
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="electronics">Electronics</button>
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="beginner">Beginner</button>
        <button class="px-6 py-2 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors font-medium" data-filter="intermediate">Intermediate</button>
      </div>

      <!-- Product Grid -->
      <div id="kits-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        <!-- Kits will be dynamically inserted here by JavaScript -->
      </div>

      <!-- Call to Action -->
      <div class="text-center mt-12">
        <p class="text-gray-600 mb-6">Want to know more?</p>
        <a href="mailto:<EMAIL>" class="inline-block bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-8 rounded-full transition-colors duration-300 shadow-md hover:shadow-lg">
          Contact Us Today!
        </a>
      </div>
    </div>

    <!-- Kits Data and Scripts -->
    <script>
      // Kits data
      const kits = [
        {
          id: 'turtle',
          name: 'Turtle Kit',
          description: 'A drawing robot that brings code to life through art. Learn programming fundamentals while creating beautiful designs.',
          image: '/public/turtleRobot.png',
          alt: 'Turtle Kit',
          link: '/turtle',
          tags: [
            { text: 'Robotics', type: 'subject' },
            { text: 'Ages 12+', type: 'age' },
            { text: 'Intermediate', type: 'difficulty' }
          ]
        },
        {
          id: 'coming-soon-1',
          name: 'New Kit Coming Soon',
          description: 'We\'re working on something exciting!',
          emoji: '🔧',
          tags: []
        },
        {
          id: 'coming-soon-2',
          name: 'New Kit Coming Soon',
          description: 'Stay tuned for updates!',
          emoji: '🚀',
          tags: []
        }
      ];

      // Render kits to the DOM
      function renderKits(filter = 'all') {
        const grid = document.getElementById('kits-grid');
        if (!grid) return;

        grid.innerHTML = ''; // Clear existing content

        kits.forEach(kit => {
          // Skip if filter doesn't match (unless it's 'all' or a coming soon kit)
          if (filter !== 'all' && kit.id.startsWith('coming-soon') === false) {
            const matchesFilter = kit.tags.some(tag => 
              tag.type === filter || tag.text.toLowerCase() === filter
            );
            if (!matchesFilter) return;
          }

          const kitElement = document.createElement('div');
          
          if (kit.id.startsWith('coming-soon')) {
            // Coming soon template
            kitElement.className = 'bg-white rounded-xl overflow-hidden shadow-md border-2 border-dashed border-gray-300';
            kitElement.innerHTML = `
              <div class="h-64 bg-gray-50 flex flex-col items-center justify-center p-8 text-center">
                <span class="text-6xl mb-4">${kit.emoji}</span>
                <h3 class="text-xl font-bold text-gray-900 mb-2">${kit.name}</h3>
                <p class="text-gray-500">${kit.description}</p>
              </div>
            `;
          } else {
            // Map tag types to colors
            const tagColors = {
              subject: 'bg-blue-100 text-blue-800',
              age: 'bg-green-100 text-green-800',
              difficulty: 'bg-yellow-100 text-yellow-800',
              default: 'bg-gray-100 text-gray-800'
            };
            
            // Generate tags HTML
            const tagsHTML = kit.tags.map(tag => {
              const colorClass = tagColors[tag.type] || tagColors.default;
              return `<span class="px-3 py-1 ${colorClass} text-sm rounded-full">${tag.text}</span>`;
            }).join('');

            // Make the entire card clickable
            kitElement.className = 'bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer';
            kitElement.innerHTML = `
              <a href="${kit.link}" class="block h-full">
                <div class="h-64 bg-gray-100 flex items-center justify-center p-8">
                  <img src="${kit.image}" alt="${kit.alt}" class="max-h-full max-w-full object-contain">
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">${kit.name}</h3>
                  <div class="flex flex-wrap gap-2 mb-4">
                    ${tagsHTML}
                  </div>
                  <p class="text-gray-600 mb-4">${kit.description}</p>
                  <div class="text-purple-600 font-medium">Learn more →</div>
                </div>
              </a>
            `;
          }
          
          grid.appendChild(kitElement);
        });
      }

      // Handle filter clicks and states
      function setupFilters() {
        const filterButtons = document.querySelectorAll('[data-filter]');
        let activeFilter = 'all'; // Track the active filter
        
        // Set initial active state
        const updateActiveFilter = (newFilter) => {
          activeFilter = newFilter;
          filterButtons.forEach(btn => {
            const isActive = btn.getAttribute('data-filter') === activeFilter;
            btn.classList.toggle('bg-purple-600', isActive);
            btn.classList.toggle('text-white', isActive);
            
            // Remove hover styles for active filter
            if (isActive) {
              btn.classList.remove('hover:bg-gray-200');
              btn.classList.add('cursor-default');
            } else {
              btn.classList.add('hover:bg-gray-200');
              btn.classList.remove('cursor-default');
            }
          });
        };
        
        // Set initial state
        updateActiveFilter('all');
        
        // Add click handlers
        filterButtons.forEach(button => {
          button.addEventListener('click', () => {
            const filter = button.getAttribute('data-filter');
            if (filter !== activeFilter) {
              updateActiveFilter(filter);
              renderKits(filter);
            }
          });
        });
      }

      // Contact modal setup
      function setupContactModal() {
        const modal = document.getElementById('contact-modal');
        const closer = document.getElementById('close-contact-modal');
        
        // Handle all contact buttons
        document.addEventListener('click', (e) => {
          const contactBtn = e.target.closest('a, button');
          if (contactBtn && (contactBtn.textContent.trim().toLowerCase() === 'contact us' || 
                             contactBtn.textContent.trim().toLowerCase() === 'contact us today!')) {
            e.preventDefault();
            modal.classList.remove('hidden');
          }
        });
        
        closer.addEventListener('click', () => modal.classList.add('hidden'));
        modal.addEventListener('click', e => {
          if (e.target === modal) modal.classList.add('hidden');
        });
      }

      // Initialize everything when the DOM is loaded
      document.addEventListener('DOMContentLoaded', () => {
        renderKits();
        setupFilters();
        setupContactModal();
      });
      
      // In case the header is loaded dynamically
      document.addEventListener('header:loaded', setupContactModal);
    </script>
  </main>

  <!-- Footer -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
</body>
</html>

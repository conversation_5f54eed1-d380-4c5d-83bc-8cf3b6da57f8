<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Kits Overview – RISE STEM</title>
  <link rel="icon" href="/public/favicon.ico"/>
  <link rel="icon" type="image/png" href="/public/favicon-32x32.png">
  <link rel="shortcut icon" href="/public/favicon.ico">
  <link rel="apple-touch-icon" href="/public/apple-touch-icon.png">

  <!-- Tailwind + font -->
  <script defer src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet"
  />

  <!-- Your custom CSS -->
  <link href="/src/style.css" rel="stylesheet">
  <link href="/src/animations.css" rel="stylesheet">

  <style>
    html { scroll-behavior: smooth; }
  </style>
</head>

<body class="flex flex-col min-h-screen font-poppins bg-gray-50">

  <!-- Header -->
  <div id="header-container"></div>
  <script type="module" src="/scripts/loadHeader.js"></script>

  <!-- Banner -->
  <section class="w-full flex items-start justify-center px-6 pt-32 bg-gray-50">
    <div class="flex justify-between items-center w-full max-w-screen-xl">
      <!-- Left Title -->
      <h1 class="text-[72px] font-extrabold text-gray-900 leading-none">
        RISE Kits
      </h1>

      <!-- Right Slogan -->
      <div class="flex flex-col justify-between text-[24px] leading-[1.2] text-gray-700 text-right h-[72px]">
        <span>Empowering tomorrow's engineers,</span>
        <span>one kit at a time.</span>
      </div>
    </div>
  </section>


  <!-- Main content -->
  <main id="kits-overview" class="flex-grow w-full">
    <div class="container mx-auto px-6 py-12">
      <!-- your kit cards/content go here -->
    </div>

    <!-- Contact Modal -->
    <div
      id="contact-modal"
      class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-start justify-center pt-20"
    >
      <div class="relative bg-white p-5 border w-96 shadow-lg rounded-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-gray-900">Contact Us</h3>
          <button
            id="close-contact-modal"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="text-sm text-gray-500">
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Phone:</strong> (470) 719‑5683</p>
        </div>
      </div>
    </div>

    <!-- Modal script -->
    <script>
      function setupContactModal() {
        const modal = document.getElementById('contact-modal');
        const closer = document.getElementById('close-contact-modal');
        document.querySelectorAll('button, a').forEach(el => {
          if (el.textContent.trim().toLowerCase() === 'contact us') {
            el.addEventListener('click', e => {
              e.preventDefault();
              modal.classList.remove('hidden');
            });
          }
        });
        closer.addEventListener('click', () => modal.classList.add('hidden'));
        modal.addEventListener('click', e => {
          if (e.target === modal) modal.classList.add('hidden');
        });
      }
      document.addEventListener('DOMContentLoaded', setupContactModal);
      document.addEventListener('header:loaded', setupContactModal);
    </script>
  </main>

  <!-- Footer -->
  <div id="footer-container"></div>
  <script type="module" src="/scripts/loadFooter.js"></script>
</body>
</html>
